/* eslint-disable max-lines */
/**
 * @file Web IDE 创建函数
 */
import {MouseEvent, ReactNode, useCallback, useMemo, useRef} from 'react';
import {isEmpty} from 'lodash';
import {useNavigate} from 'react-router-dom';
import {Modal} from '@panda-design/components';
import {UnknownObject} from '@/types/icode/common';
import {APP_BASENAME, ICODING_APP_SCHEME, ICODING_ORIGIN} from '@/constants/icode/urls';
import {ensureSSHKeyAdded} from '@/utils/icode/ensureSSHKeyAdded';
import {apiPostCreateFromCode, CreateFromCodeParams, ResourceStatusInfo} from '@/api/icode/webIDE';
import {handleStopPropagation} from '@/constants/icode/handlers';
import {FailureKind, usePaymentAccountSelectModal} from './usePaymentAccountSelectModal';

const params = {
    standbyTime: 60,
};

/* eslint-disable @typescript-eslint/no-explicit-any */
interface WebIDEResourceLinkProps {
    modal: any;
    children?: ReactNode;
}

const WebIDEResourceLink = (props: WebIDEResourceLinkProps) => {
    const navigate = useNavigate();
    const handleClick = useCallback(
        (e: MouseEvent) => {
            e.preventDefault();
            props.modal.destroy();
            navigate('/dev/resource');
        },
        [navigate, props.modal]
    );
    return (
        // eslint-disable-next-line no-script-url
        <a href="javascript:void(0)" onClick={handleClick}>{props.children}</a>
    );
};

// 处理 9002 和 9011 错误码的 hook，含弹窗
export const useResourceErrorModal = () => {
    const [modal, contextHolder] = Modal.useModal();
    const errorHandler = useCallback(
        (e: any) => {
            const modalController = modal.error({});
            const content = (() => {
                // RESOURCES_COUNT_LIMIT
                if (e.responseCode === 9011) {
                    return '小伙伴们，非常抱歉，当前云 IDE 容器资源已满，请稍候重试';
                }
                // PERSONAL_RESOURCE_COUNT_LIMIT
                if (e.responseCode === 9002) {
                    return (
                        <>
                            您运行中的代码空间已达上限，如需创建，请前往
                            <WebIDEResourceLink modal={modalController}>
                                个人 WebIDE 代码空间管理
                            </WebIDEResourceLink>
                            挂起一些代码空间。
                        </>
                    );
                }
                return e.message;
            })();
            modalController.update({content});
        },
        [modal]
    );
    return [errorHandler, contextHolder] as const;
};

type CreatingResourceApiFn<T> = (args: T) => Promise<string>;

export interface EffectOption {
    keepCurrentTab?: boolean;
}

export type CreatingResourceFn = (extraParams?: UnknownObject, option?: EffectOption) => Promise<void>;

interface WithCodeRepo {
    codeRepo: string;
}

// 高阶函数，用于兼容多种 createResourceApi 函数
// 例如，iCode、Cov
function createResourceCreatingHook<T extends WithCodeRepo>(
    apiFn: CreatingResourceApiFn<T>
) {
    return function useResourceCreating(args: T) {
        const [errorHandler, contextHolder1] = useResourceErrorModal();
        const [ensurePaymentAccountSelected, contextHolder2] = usePaymentAccountSelectModal();
        // 防止多次点击，发出多个请求
        // @TODO useWaterfall 出来以后换成 useWaterfall
        const count = useRef(0);
        const createResource = useCallback<CreatingResourceFn>(
            async (extraParams?: UnknownObject, option?: EffectOption) => {
                try {
                    if (count.current > 0) {
                        return;
                    }
                    count.current++;
                    // 确保用户已经绑定了用于代码空间计费的云上百度资源账户
                    try {
                        await ensurePaymentAccountSelected({repoName: args.codeRepo});
                    }
                    catch (e) {
                        if (e.message === FailureKind.userCancelled) {
                            count.current--;
                            return;
                        }
                        if (e.message === FailureKind.accountCheckFailed) {
                            throw new Error('未能创建代码空间：资源账户绑定状态查询失败');
                        }
                        if (e.message === FailureKind.accountBindFailed) {
                            throw new Error('未能创建代码空间：资源账户绑定失败');
                        }
                        throw e;
                    }
                    // ensureSSHKeyAdded 用于保证容器中已经存在合法的用户 ssh key
                    // 这段逻辑是串行的，因为 apiFn 触发后端创建代码空间
                    // 后端创建时需要 clone 代码，这个逻辑依赖 ssh key added
                    await ensureSSHKeyAdded();
                    const jumpUri = await apiFn({...args, ...extraParams});
                    if (jumpUri) {
                        if (option && option.keepCurrentTab) {
                            window.location.replace(jumpUri);
                        }
                        else {
                            window.open(jumpUri);
                        }
                    }
                    count.current--;
                }
                catch (e) {
                    errorHandler(e);
                    count.current--;
                }
            },
            [ensurePaymentAccountSelected, args, errorHandler]
        );

        const contextHolder = useMemo(
            () => (
                <span onClick={handleStopPropagation}>
                    <span key="contextHolder1">{contextHolder1}</span>
                    <span key="contextHolder2">{contextHolder2}</span>
                </span>
            ),
            [contextHolder1, contextHolder2]
        );

        return [createResource, contextHolder] as const;
    };
}

// make some properties as optional
type Optional<T, K extends keyof T> = Omit<T, K> & Partial<T>;

/**
 * 如果status.swanEditorUrl字段为空，就拼一个url，否则直接用swanEditorUrl
 * @param status ResourceStatusInfo
 * @param query optional
 */
export const getResourceICodingEditorLink = (status: ResourceStatusInfo, query?: URLSearchParams) => {
    return isEmpty(status.swanEditorUrl)
        ? `${ICODING_ORIGIN}/workbench/${status.mid}${query ? `/?${query}` : ''}`
        : status.swanEditorUrl;
};

/**
 * 创建开发容器
 *
 * @param args
 */
const useCreateFromCode = createResourceCreatingHook(
    // eslint-disable-next-line complexity
    async (params: CreateFromCodeParams) => {
        const status = await apiPostCreateFromCode(params);
        if (status.status === '0') {
            if (params.templateId === 21) {
                params.openAiIdeCallback?.(status.resourceId);
                return;
            }
            const query = new URLSearchParams();
            if (status.path) {
                query.set('folder', status.path);
            }
            if (params.pullType === 'diff' && params.reviewStatus === 'NEW') {
                const extQuery = new URLSearchParams();
                const [changeNumber] = params.pullValue.split('-');
                extQuery.set('repo', params.codeRepo);
                extQuery.set('changeId', changeNumber);
                query.set('extUrl', `${ICODING_APP_SCHEME}://baidu.icode-code-review/revealChange?${extQuery}`);
            }
            else if (status.initPath) {
                query.set('extUrl', `${ICODING_APP_SCHEME}://baidu.icode/revealPath?path=${status.initPath}`);
            }
            query.set('codeSpaceKind', params.codeSpaceKind || params.templateId === 21 ? 'aiIDE' : undefined);
            return getResourceICodingEditorLink(status, query);
        }

        if (params.pullType !== 'diff' || params.reviewStatus !== 'NEW') {
            const query = new URLSearchParams();
            query.set('codeSpaceKind', params.codeSpaceKind || params.templateId === 21 ? 'aiIDE' : undefined);
            return `${APP_BASENAME}/dev/resource/creating/${status.resourceId}?${query}`;
        }

        const [changeNumber] = params.pullValue.split('-');
        const query = new URLSearchParams();
        query.set('codeSpaceKind', params.codeSpaceKind || params.templateId === 21 ? 'aiIDE' : undefined);
        query.set('repo', params.codeRepo);
        query.set('changeId', changeNumber);
        return `${APP_BASENAME}/dev/resource/creating/${status.resourceId}?${query}`;
    }
);

/**
 * 创建开发容器，提供默认参数
 *
 * @param args
 */
export const useCreateResourceCallback = (
    args: Optional<CreateFromCodeParams, 'standbyTime'>
) => useCreateFromCode({...params, ...args});
