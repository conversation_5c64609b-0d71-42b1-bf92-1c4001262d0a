import type { SVGProps } from "react";
const SvgLinux = (props: SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" {...props}>
        <g fill="none">
            <path
                fill="#09395D"
                d="M.12 19.946c0 10.855 8.86 19.655 19.791 19.655 10.93 0 19.792-8.8 19.792-19.655S30.842.291 19.91.291C8.981.29.119 9.09.119 19.946Z"
            />
            <path
                fill="#1C89BB"
                d="M31.109 3.74c11.719 8.447 11.458 25.258-.434 32.844-.695.431-1.476 1.38-2.344.862-1.042-.517-.52-1.552-.434-2.414.174-2.672-.434-5.258-1.389-7.758-1.736-4.828.087-7.587 5.295-8.19 1.563-.172 3.299.259 5.556-1.034-5.035-1.638-8.507-4.31-10.938-8.276-.954-3.707 1.042-6.293 4.688-6.035ZM13.66 21.066c4.6 3.448 8.767 7.155 9.81 13.19.78 4.568-.175 5.775-4.688 5.517-8.16-.518-13.976-4.483-17.188-11.983-.174-1.12.52-1.897 1.302-2.5 2.517-2.241 5.642-3.362 8.68-4.741.695-.345 1.476-.087 2.084.517"
            />
            <path
                fill="#55A7CC"
                d="M31.109 3.74c-2.344 1.379-3.733 3.534-4.688 6.034-5.902-3.793-11.371-3.535-16.927.69-2.777 2.068-4.948 4.74-7.03 7.5-.609.861-.956 2.068-2.258 2.24C-.922 14.602 2.984 7.016 8.974 3.223c6.944-4.397 15.277-4.224 22.135.517"
            />
            <path
                fill="#55A7CC"
                d="M13.661 21.067c-3.993 2.241-8.42 3.793-12.066 6.724-.434-4.914 7.379-15.345 13.02-16.38-2.343 3.277-3.905 6.38-.954 9.656M23.73 10.98c0 .334.273.604.608.604.336 0 .608-.27.608-.603a.606.606 0 0 0-.608-.604.606.606 0 0 0-.608.604Z"
            />
        </g>
    </svg>
);
export default SvgLinux;
