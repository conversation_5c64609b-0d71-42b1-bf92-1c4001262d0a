import {Language, Parser, Query, QueryMatch} from 'web-tree-sitter';
import {createMappedRegion} from 'region-react';
import {LanguageType, languageQueryMap} from './languages';

const publicPath = 'https://now.bdstatic.com/stash/v1/db94409/comate-stack-fe/6caa38c/tree-sitter-0.23.1/';

const getParserPromise = (): Promise<Parser> => {
    return new Promise(resolve => {
        Parser.init({
            locateFile: (scriptName: string) => publicPath + scriptName,
        }).then(() => {
            const parser = new Parser();
            resolve(parser);
        });
    });
};

const parserPromise = getParserPromise();

interface InstanceMap {
    [language: string]: Language;
}
const instanceMap: InstanceMap = {};

async function loadLanguage(language: LanguageType) {
    if (instanceMap[language]) {
        return instanceMap[language];
    }

    try {
        const instance = await Language.load(`${publicPath}tree-sitter-${language}.wasm`);
        instanceMap[language] = instance;
        return instance;
    }
    catch (e) {
        // do nothing
    }
}

interface Key {
    sourceCode: string;
    language: LanguageType;
}

const region = createMappedRegion<Key, QueryMatch[]>();

const getMatches = region.getValue;

const setMatches = region.set;

// 执行查询，返回匹配的节点
export async function query(sourceCode: string, language: LanguageType): Promise<QueryMatch[]> {
    const cache = getMatches({sourceCode, language});
    if (cache) {
        return cache;
    }
    if (sourceCode.split('\n').length > 2000) {
        return [];
    }
    try {
        const parser = await parserPromise;
        const instance = await loadLanguage(language);
        parser.setLanguage(instance);
        const tree = parser.parse(sourceCode);
        const query = languageQueryMap[language];
        const q = new Query(instance, query);
        const matches = q.matches(tree.rootNode);
        setMatches({sourceCode, language}, matches);
        return matches;
    }
    catch (e) {
        return [];
    }
}
