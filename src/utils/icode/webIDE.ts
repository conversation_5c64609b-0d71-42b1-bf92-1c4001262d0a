import {
    CODE_SPACE_KIND_MAP,
    REAL_TEMPLATE_ID_MAP,
    NAMED_TEMPLATE_ID_MAP,
    NAMED_TEMPLATE_ID_DISPLAY_NAME,
} from '@/constants/icode/webIDE';
import {
    CodeSpaceKind,
    IdeType,
    NamedTemplateId,
    OperatingSystem,
} from '@/types/icode/webIDE';
import {RepoInfo} from '@/types/icode/repo';

interface Params {
    languageReal?: RepoInfo['language'] | null;
    templateName?: RepoInfo['templateName'] | null;
    ideType?: IdeType | null;
}

// 根据代码库的语言与模版配置，获得Web IDE创建的镜像名称和模版ID
export const getCodeSpaceTemplateIdByRepoInfo = (params: Params): number => {
    const {languageReal, templateName, ideType} = params;
    // 当代码库模版是 javascript-swan 时，选用 swan 的镜像
    if (languageReal === 'javascript' && templateName === 'javascript-swan') {
        return REAL_TEMPLATE_ID_MAP.swan;
    }
    // 当选择的 IDE 类型是 IDEA 时，选用 IDEA 的镜像
    if (ideType === 'ideaCommunity') {
        return REAL_TEMPLATE_ID_MAP['idea-community-centos-7u5'];
    }
    // 当选择的 IDE 类型是 PyCharm 时，选用 IDEA 的镜像
    if (ideType === 'pycharm') {
        return REAL_TEMPLATE_ID_MAP['pycharm-community-centos-7u5'];
    }
    // 当选择的 IDE 类型时 GoLand 时，选用 GoLand 的镜像
    if (ideType === 'goland') {
        return REAL_TEMPLATE_ID_MAP['goland-community-centos-7u5'];
    }
    // Rust 代码库使用 Rust 的镜像
    if (languageReal === 'rust') {
        return REAL_TEMPLATE_ID_MAP.rust;
    }
    // 其余情况，都选用 aiIDE 的镜像
    return REAL_TEMPLATE_ID_MAP.aiIDE;
};

export const getCodeSpaceTemplateId = (codeSpaceKind: CodeSpaceKind, os?: OperatingSystem): number => {
    if (codeSpaceKind === 'ideaCommunity') {
        if (os === 'centos-7u5') {
            return REAL_TEMPLATE_ID_MAP['idea-community-centos-7u5'];
        }
        return REAL_TEMPLATE_ID_MAP['idea-community-ubuntu'];
    }
    if (codeSpaceKind === 'ideaUltimate') {
        if (os === 'centos-7u5') {
            return REAL_TEMPLATE_ID_MAP['idea-ultimate-centos-7u5'];
        }
        return REAL_TEMPLATE_ID_MAP['idea-ultimate-ubuntu'];
    }
    if (codeSpaceKind === 'vscode') {
        if (os === 'centos-7u5') {
            return REAL_TEMPLATE_ID_MAP['vscode-centos-7u5'];
        }
        return REAL_TEMPLATE_ID_MAP['vscode-centos-6u3'];
    }
    if (codeSpaceKind === 'aiIDE') {
        return REAL_TEMPLATE_ID_MAP.aiIDE;
    }
    const mapping: Partial<Record<CodeSpaceKind, NamedTemplateId>> = {
        'androidStudio': 'android-studio-community-centos-7u5',
        'pycharm': 'pycharm-community-centos-7u5',
        'rust': 'rust',
        'swan': 'swan',
        'goland': 'goland-community-centos-7u5',
    };
    const namedTemplateId: NamedTemplateId = mapping[codeSpaceKind] ?? 'vscode-centos-6u3';
    return REAL_TEMPLATE_ID_MAP[namedTemplateId];
};

export const getCodeSpaceKind = (templateId: number | string | null): CodeSpaceKind => {
    const namedTemplateId = NAMED_TEMPLATE_ID_MAP[`${templateId}`] ?? 'vscode-centos-6u3';
    return CODE_SPACE_KIND_MAP[namedTemplateId];
};

export const getCodeSpaceKindDisplayName = (templateId: number | string | null): string => {
    const namedTemplateId = NAMED_TEMPLATE_ID_MAP[`${templateId}`] ?? 'vscode-centos-6u3';
    return NAMED_TEMPLATE_ID_DISPLAY_NAME[namedTemplateId];
};
