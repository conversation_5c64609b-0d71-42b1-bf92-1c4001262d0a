/**
 * @file WebIDE相关的工具方法
 */
import {createRegion} from 'region-react';
import {once} from 'lodash';
import {apiGetSSHKeys, apiPostSSHKey} from '@/api/icode/account';
import {apiGetSSHPublicKey} from '@/api/icode/webIDE';

const flagRegion = createRegion(
    false,
    {withLocalStorageKey: 'ICODE/ideSshKeyAdded'}
);

// 验证icoding的SSHKey是否已经添加
// 如果后续逻辑中包括床架代码空间相关逻辑，需要串行执行
// 以保证创建代码空间 clone 代码的阶段服务端拥有可用的用户 ssh key
// 出于性能优化考虑，once 这里保证函数仅执行一次
export const ensureSSHKeyAdded = once(async () => {
    // flag 保证下面的请求可以在刷新后依然只有一次
    const flag = flagRegion.getValue();
    if (flag) {
        // istanbul ignore next since once
        return;
    }
    const [{publicKey}, sshKeys] = await Promise.all([
        apiGetSSHPublicKey(),
        apiGetSSHKeys(),
    ]);
    const publicKeyAdded = sshKeys.some(key => key.ssh_public_key === publicKey);
    if (!publicKeyAdded) {
        await apiPostSSHKey(publicKey);
        flagRegion.set(true);
    }
});
