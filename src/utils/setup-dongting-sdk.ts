import {setupDongTingSDKV2} from '@befe/dongting-sdk-v2-bootstrap';
import {DongTingSDKV3} from '@befe/dongting-sdk-v2-bootstrap/types/src/common/common-public-type';
import {fetchTokenForAppSDK} from '@/api/staff';
import {APP_IS_ONLINE_PRODUCTION} from '@/constants/app';

// 以下是一个关键点 : 你需要判断, 当前的环境是测试环境还是正式环境
// 测试环境 => 应用测试环境的 sdk, 生产环境 => 应用生产环境的 sdk
//
// 例如 : 你可以用产品的 "hostname" 精准匹配进行判断, 或者你的构建中, 若有环境相关的参数, 也可以用
const isProd = APP_IS_ONLINE_PRODUCTION;

const {
    // sdk 的主体实例
    getSDK,

    // 用于上报入参数据给 "策略模型" (提供推荐的卡片)
    getDataChannel,
} = setupDongTingSDKV2({
    // 默认是 2: 浮层式
    // 3支持嵌入式 和 浮层式
    sdkVersion: 3, // 3
    // 这个是要接入辅助区的业务的 app id, 在新洞听后台中, 由管理员获取
    // 请务必确认 prod 和 dev 环境使用的 app id 是不一样的
    appId: isProd ? '23210c27884f76dbd858a8726f527410' : 'bcb602cf6a2d14bc36aa174078326825',
    mapPageURL: (url: string) => {
        /**
         * return
         *     - [url, 'float']: 浮层式模式
         *     - [url, 'embed']: 嵌入式模式
         */
        return [url, 'float'];
    },
    // 这个要依赖后端 RD 提供给你的获得 ugate token 的 api
    // 该入参, 最终形式为 : () => Promise<string>
    getUGateToken: async () => {
        try {
            return await fetchTokenForAppSDK('uuapclient-842773358683377665-wclkN-beta');
        } catch (error) {
            console.error('获取UGate Token失败:', error);
            throw error;
        }
    },

    // ugate token 的有效时间, 单位 (秒), 在失效前 10 秒, 会自动刷新 token
    // 默认 : 7200
    ugateTokenReloadInternal: 7200,

    // ======================== 🚀 重要注意事项 🚀 ===========================
    // 注意 : 如果接入方使用的是 erp8050 / erp8070
    // 需替换下面的 'dev' 为 'test_8050' / 'test_8070'
    // ====================================================================
    sdkEnv: isProd ? 'prod' : 'dev', // <===== 'dev' 应为 'test_8050' 或 'test_8070' (见上方说明)

    // 此项是为了保证辅助区不要遮挡住你的 app 的顶部导航条
    // 默认为 60, 如果一致的话, 就不用提供了 ( 提示 : 如果是用 brick 组件库, 一般都是 60px )
    appHeaderHeight: 60,
});

// ts 接入需要约束 指定 getSDK().then(sdk => {}) sdk 的类型约束
const noopPromise = () => new Promise(() => {});
const getSDKV3 = (getSDK || noopPromise) as () => Promise<DongTingSDKV3>;

// 本文件, 将 `setupDongTingSDKV2` 所输出的 "getSDK" 导出, 并重命名为 getDongtingSDK
// 同理, 见 dataChannel 相关 (上报推荐请求)
// 推荐本方式, 是利用 es 模块的处理方案, 提供出一个全局可 import 的 named util
// 同时, 一旦有引用, 则会自动 setup 辅助区 (如无引用, 则不会也不应该 setup 辅助区)
// @todo: 这个有一定的 tree shaking 能力才能按预期运行, 否则总是会尝试 setup
export {
    getSDKV3 as getDongTingSDKV3,
    getDataChannel as getDongTingDataChannel,
};
