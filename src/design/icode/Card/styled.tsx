import styled from '@emotion/styled';
import {myColors, myToken} from '@/constants/colors';

interface BasicCardProps {
    disabled?: boolean;
}

export const BasicCard = styled.div<BasicCardProps>`
    width: 100%;
    height: 240px;
    padding: 16px;
    pointer-events: ${({disabled}) => (disabled ? 'none' : 'auto')};
    border: 1px solid transparent;
    border-radius: ${myToken.borderRadius};
    box-shadow: 0 0 11px 0 ${myColors.blackTransparent15};
    background: ${({disabled}) => (disabled ? 'var(--color-gray-2)' : 'var(--color-gray-1)')};
    display: flex;
    flex-direction: column;
    ${({disabled}) => disabled && 'color: var(--color-gray-7);'}
`;
