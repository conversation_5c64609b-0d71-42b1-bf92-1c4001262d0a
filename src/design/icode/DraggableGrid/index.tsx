/* eslint-disable max-lines */
/**
 * @file 可拖拽的 Gird 布局，拖拽顺序持久化在 localStorage
 */
import {FC, CSSProperties, useCallback, useMemo, ReactNode} from 'react';
import {
    DndContext,
    closestCenter,
    PointerSensor,
    useSensor,
    useSensors,
    DragEndEvent,
} from '@dnd-kit/core';
import {restrictToParentElement} from '@dnd-kit/modifiers';
import {
    arrayMove,
    useSortable,
    SortableContext,
    rectSortingStrategy,
} from '@dnd-kit/sortable';
import {CSS} from '@dnd-kit/utilities';
import {createMappedRegion} from 'region-core';
import {sortBy} from 'lodash';
import Grid from '@/design/icode/Grid';
import {rearrangeWith} from './utils';

interface DraggableCardProps {
    id: string;
    children?: ReactNode;
    style?: CSSProperties;
}

// 用于包裹可拖拽元素的 div，负责接受各种 listener，防止相互影响
const DraggableCard = ({id, children, style}: DraggableCardProps) => {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging,
    } = useSortable({id});

    const mergedStyle: CSSProperties = {
        transform: CSS.Transform.toString(transform),
        transition: transition ? transition : undefined,
        // 当前拖拽的元素要能盖住别的元素
        zIndex: isDragging ? 1 : undefined,
        ...style,
    };

    return (
        <div ref={setNodeRef} style={mergedStyle} {...attributes} {...listeners}>
            {children}
        </div>
    );
};

interface DraggableDataItem {
    id: string;
}

interface SortableItemComponentProps<T extends DraggableDataItem> {
    itemData: T;
}

interface DraggableGridProps<T extends DraggableDataItem> {
    // local storage 用于存储的 key
    storageKey: string;
    // 数据源，需要存在一个 id
    dataSource: T[];
    // 被拖拽排序的 Item 组件
    SortableItemComponent: FC<SortableItemComponentProps<T>>;
    leaveFirstCellBlank?: boolean;
    className?: string;
}

// 通过 region 的 local storage 能力访问 localStorage
const draggableGridOrderMapRegion = createMappedRegion<string, string[]>(
    [],
    {withLocalStorageKey: 'ICODE/draggableGridOrderMap'}
);

const DraggableGrid = <T extends DraggableDataItem>(props: DraggableGridProps<T>) => {
    const {dataSource, SortableItemComponent, storageKey, leaveFirstCellBlank = false} = props;
    // sensors 是 dnd kit 对于各种用户交互事件的抽象
    // 包括 PointerSensor 和 KeyboradSensor
    const sensors = useSensors(
        useSensor(PointerSensor, {
            // 在 drag 开始后，sortable item 上会被叠加 drag overlay component，阻止了其 onClick 事件的触发（预期行为）。
            // 因此我们不想一开始就激活 drag，于是设置了以下激活条件，让 sortable item 在未被拖动时能够响应用户的点击。
            // refer to: https://github.com/clauderic/dnd-kit/issues/355#issuecomment-874881817
            activationConstraint: {
                distance: 10,
            },
        })
    );

    // 存储在 localStorage 中的 id 顺序列表
    const storedIdList = draggableGridOrderMapRegion.useValue(storageKey);

    const renderedIdList = useMemo(
        () => {
            // 首先把输入数据 map 成包含顺序的 id
            const dataIdList = dataSource.map(item => item.id);

            // 如果没有 localStorage，以id排序即可，我们依然想要排个序，这样可以保证元素的位置不会因dataSource里位置改变而变化，视图位置相对固定
            if (storedIdList.length === 0) {
                return sortBy(dataIdList);
            }

            // 这里逻辑有点复杂，看了代码再概括下，比如 localStorage 是 3,2,1，后端给 2,3,4，结果应该是 3,2,4
            return rearrangeWith(dataIdList, storedIdList);
        },
        [dataSource, storedIdList]
    );

    const handleDragEnd = useCallback(
        (e: DragEndEvent) => {
            const {active, over} = e;
            if (!over || over.id === active.id) {
                return;
            }
            // 理论上来讲，这里不应该用这么多 find，包括下面也是
            // 但是我懒得搞了，如果哪天渲染慢了再说
            // 目前的应用场景来看，这个 list 不会很大，顶多 60 个元素
            const oldIndex = renderedIdList.indexOf(String(active.id));
            const newIndex = renderedIdList.indexOf(String(over.id));
            draggableGridOrderMapRegion.set(storageKey, arrayMove(renderedIdList, oldIndex, newIndex));
        },
        [renderedIdList, storageKey]
    );

    return (
        <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
            modifiers={[restrictToParentElement]}
        >
            <SortableContext
                items={renderedIdList}
                strategy={rectSortingStrategy}
            >
                <Grid>
                    {renderedIdList.map((id, index) => (
                        <DraggableCard
                            key={id}
                            id={id}
                            // 如果需要空出第一个cell，则第一个card的column start应该是2
                            style={(index === 0 && leaveFirstCellBlank) ? {gridColumnStart: 2} : {}}
                        >
                            <SortableItemComponent
                                key={id}
                                // 同上，这里是懒得搞 map 了
                                itemData={dataSource.find(item => item.id === id)}
                            />
                        </DraggableCard>
                    ))}
                </Grid>
            </SortableContext>
        </DndContext>
    );
};

export default DraggableGrid;
