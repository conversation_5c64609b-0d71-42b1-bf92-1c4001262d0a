/**
 * @file 提示横幅组件
 */
import {ReactNode} from 'react';
import {IconInfoCircleO} from '@baidu/ee-icon';
import {Alert} from 'antd';
import {css} from '@emotion/css';

const iconCss = css`
    width: 18px !important;
    height: 18px !important;
    top: 8px;
    left: 12px;
`;

interface Props {
    type?: 'warning' | 'info' | 'error';
    className?: string;
    children?: ReactNode;
    icon?: ReactNode;
    closable?: boolean;
}

const Notice = ({type = 'info', className, icon, children, closable = true}: Props) => {
    return (
        <Alert
            banner
            closable={closable}
            icon={icon ?? <IconInfoCircleO className={iconCss} />}
            type={type}
            message={children}
            className={className}
        />
    );
};

export default Notice;
