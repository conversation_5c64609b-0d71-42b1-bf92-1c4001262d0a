import {createMappedRegion} from 'region-react';
import {apiWorkflowQuery, WorkflowQueryParams, WorkflowQueryResponse} from '@/api/ievalue/prompt';
const searchAllWorkflowRegion = createMappedRegion<WorkflowQueryParams, WorkflowQueryResponse>();

export const useSearchAllWorkflow = searchAllWorkflowRegion.useValue;
export const getSearchAllWorkflow = searchAllWorkflowRegion.getValue;
export const useSearchAllWorkflowLoadingByRegion = searchAllWorkflowRegion.useLoading;

export const loadAllWorkflow = searchAllWorkflowRegion.loadBy(
    params => params,
    apiWorkflowQuery
);
