import {createRegion} from 'region-react';

const region = createRegion<NodeJS.Timeout>();

export const useTimeoutByRegion = region.useValue;

export const setTimeoutByRegion = region.set;

export const getTimeoutByRegion = region.getValue;


export const clearTimeoutByRegion = () => {
    const timeout = region.getValue();
    if (timeout) {
        clearTimeout(timeout);
        region.reset();
    }
};

