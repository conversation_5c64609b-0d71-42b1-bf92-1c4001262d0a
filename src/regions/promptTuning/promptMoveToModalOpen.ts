import {createRegion} from 'region-react';
import {DirItem} from '@/api/ievalue/promptDir';

interface Props {
    refresh?: () => void;
    dirItem: DirItem;
}

const promptMoveToModalOpenRegion = createRegion<Props>();

export const usePromptMoveToModalProps = promptMoveToModalOpenRegion.useValue;

export const openPromptMoveToModalWithProps = promptMoveToModalOpenRegion.set;

export const closePromptMoveToModal = promptMoveToModalOpenRegion.reset;
