import {createMappedRegion} from 'region-react';
import {apiPromptList, PromptListParams, PromptModel} from '@/api/ievalue/prompt';
import {Paginated} from '@/api/ievalue/prompt-version';

const searchPromptsRegion = createMappedRegion<PromptListParams, Paginated<PromptModel>>();

export const useSearchPrompts = searchPromptsRegion.useValue;
export const getSearchPrompts = searchPromptsRegion.getValue;
export const useSearchPromptsLoadingByRegion = searchPromptsRegion.useLoading;

export const loadPrompts = searchPromptsRegion.loadBy(
    params => params,
    apiPromptList
);
