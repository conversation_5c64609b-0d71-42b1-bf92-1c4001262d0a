import {createMappedRegion} from 'region-react';
import {apiModelListPrompt, ModelListPromptParams, ModelListResponse} from '@/api/ievalue/model';

const modelListRegion = createMappedRegion<ModelListPromptParams, ModelListResponse>();

export const getModalList = modelListRegion.getValue;

export const useModelList = modelListRegion.useValue;

export const useModelListLoading = modelListRegion.useLoading;

export const loadModelList = modelListRegion.loadBy(
    params => params,
    apiModelListPrompt
);
