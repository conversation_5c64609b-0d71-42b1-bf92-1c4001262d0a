import {createMappedRegion} from 'region-react';
import {apiPromptQualityOptimize, PromptQualityOptimizeResponse} from '@/api/ievalue/prompt';

const promptQualityOptimizeRegion = createMappedRegion<number, PromptQualityOptimizeResponse>(
    undefined,
    {startLoadingWith: false}
);

export const usePromptQualityOptimizeLoading = promptQualityOptimizeRegion.useLoading;

export const usePromptQualityOptimize = promptQualityOptimizeRegion.useValue;

export const getPromptQualityOptimize = promptQualityOptimizeRegion.getValue;

export const setPromptQualityOptimize = promptQualityOptimizeRegion.set;

export const loadPromptQualityOptimize = promptQualityOptimizeRegion.loadBy(
    params => params.qualityID,
    apiPromptQualityOptimize
);
