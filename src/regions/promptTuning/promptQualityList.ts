import {createMappedRegion} from 'region-react';
import {
    apiPromptQualityList,
    PromptQualityListItem,
} from '@/api/ievalue/prompt';
import {Paginated} from '@/api/ievalue/prompt-version';

const promptQualityListRegion = createMappedRegion<
    { spaceCode: string, versionID?: number, promptID?: number },
    Paginated<PromptQualityListItem>
>();

export const usePromptQualityListLoading = promptQualityListRegion.useLoading;

export const usePromptQualityList = promptQualityListRegion.useValue;

export const loadPromptQualityList = promptQualityListRegion.loadBy(
    params => ({spaceCode: params.spaceCode, versionID: params.versionID, promptID: params.promptID}),
    apiPromptQualityList
);
