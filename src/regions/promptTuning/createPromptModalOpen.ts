import {createRegion} from 'region-react';
import {PromptVariable, PromptVersionVersionItem} from '@/api/ievalue/prompt-version';
import {ImageListItem, PromptModel, UploadFileItem} from '@/api/ievalue/prompt';
import {ITemplateSquareModel} from '@/api/ievalue/home';
import {SpaceItem} from '@/api/ievalue/space';

interface Props {
    refresh?: () => void;
    type?: 'prompt' | 'flow' | 'promptWithoutSpaceCode';
    template?: ITemplateSquareModel;
    showSuccessLink?: (data: PromptModel) => void;
    text?: string;
    system?: string;
    variables?: PromptVariable[];
    // onClose?: () => void;
    data?: PromptModel | PromptVersionVersionItem;
    templateInfo?: ITemplateSquareModel;
    spaceList?: SpaceItem[];
    promptName?: string;
    dirID?: number;
    imageList?: ImageListItem[];
    multiModal?: UploadFileItem[];
}

const createPromptModalOpenRegion = createRegion<Props>();

export const useCreatePromptModalProps = createPromptModalOpenRegion.useValue;

export const openCreatePromptModalWithProps = createPromptModalOpenRegion.set;

export const closeCreatePromptModal = createPromptModalOpenRegion.reset;
