import {createRegion} from 'region-react';
import {apiPromptEvolveInfo, PromptEvolveResultInfo} from '@/api/ievalue/prompt';
import {PromptEvolveStatusEnum} from '@/constants/ievalue/prompt';
import {clearTimeoutByRegion, setTimeoutByRegion} from './timeourRef';

const region = createRegion<PromptEvolveResultInfo>();

const getPromptEvolveInfo = region.getValue;

export const usePromptEvolveInfo = region.useValue;

export const setPromptEvolveInfo = region.set;

export const resetPromptEvolveInfo = region.reset;

const loadPromptEvolveInfo = region.loadBy(
    apiPromptEvolveInfo
);

export const getPromptEvolveInfoIsPending = (status: PromptEvolveStatusEnum) => [
    PromptEvolveStatusEnum.RUNNING,
    PromptEvolveStatusEnum.WAITING,
].includes(status);

export const startPollingPromptEvolveInfo = async (evolveID: number) => {
    await loadPromptEvolveInfo({evolveID});
    const result = getPromptEvolveInfo();
    clearTimeoutByRegion();
    const isFinal = [
        PromptEvolveStatusEnum.SUCCESS,
        PromptEvolveStatusEnum.FAIL,
    ].includes(result?.status);
    if (isFinal || !result) {
        // nothing
    }
    else {
        const timer = setTimeout(() => startPollingPromptEvolveInfo(evolveID), 10000);
        setTimeoutByRegion(timer);
    }
};
