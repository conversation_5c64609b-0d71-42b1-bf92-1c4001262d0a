import {createRegion} from 'region-react';
import {DirItem} from '@/api/ievalue/promptDir';

interface Props {
    refresh?: () => void;
    dirItem: DirItem;
}

const dirMoveToModalOpenRegion = createRegion<Props>();

export const useDirMoveToModalProps = dirMoveToModalOpenRegion.useValue;

export const openDirMoveToModalWithProps = dirMoveToModalOpenRegion.set;

export const closeDirMoveToModal = dirMoveToModalOpenRegion.reset;
