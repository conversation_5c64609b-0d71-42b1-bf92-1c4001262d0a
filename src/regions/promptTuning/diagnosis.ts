import {createMappedRegion} from 'region-react';
import {apiPromptQualityRun, QualityResult} from '@/api/ievalue/prompt';

interface Result {
    ID: number;
    result: QualityResult[];
    text: string;
}

const diagnosisRegion = createMappedRegion<
    string,
    Result
>(undefined, {
    startLoadingWith: false,
});

export const useDiagnosisLoading = diagnosisRegion.useLoading;

export const useDiagnosis = diagnosisRegion.useValue;

export const setDiagnosis = diagnosisRegion.set;

export const resetDiagnosis = diagnosisRegion.reset;

export const resetDiagnosisAll = diagnosisRegion.resetAll;

export const loadDiagnosis = diagnosisRegion.loadBy(
    params => (params.spaceCode),
    apiPromptQualityRun,
    (_, response, params) => {
        const {ID, result} = response;
        return {
            ID,
            result,
            text: params.text,
        };
    }
);
