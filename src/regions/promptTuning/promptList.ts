import {createMappedRegion} from 'region-react';
import {apiPromptList, PromptModel} from '@/api/ievalue/prompt';
import {Paginated} from '@/api/ievalue/prompt-version';

interface Key {
    promptName?: string;
    spaceCode: string;
}

const promptListRegion = createMappedRegion<Key, Paginated<PromptModel>>();

export const usePromptList = promptListRegion.useValue;

export const usePromptListLoadingByRegion = promptListRegion.useLoading;

const fetchAllPromptList = async ({spaceCode, promptName}: Key) => {
    const {list, total} = await apiPromptList({
        promptName: promptName ?? '',
        spaceCode: spaceCode,
        promptType: 'NORMAL',
        pn: 1,
        size: 1000,
    });
    return {list, total};
};

export const loadPromptList = promptListRegion.loadBy(
    ({spaceCode, promptName}) => ({spaceCode, promptName}),
    fetchAllPromptList
);
