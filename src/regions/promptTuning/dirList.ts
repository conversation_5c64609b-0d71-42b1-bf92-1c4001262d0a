import {createMappedRegion} from 'region-react';
import {DirItem, DirListParams, apiDirList} from '@/api/ievalue/promptDir';

const dirListRegion = createMappedRegion<DirListParams, DirItem[]>([]);

export const useDirList = dirListRegion.useValue;
export const useDirListLoading = dirListRegion.useLoading;
export const getDirListValue = dirListRegion.getValue;
export const setDirListValue = dirListRegion.set;
export const loadDirList = dirListRegion.loadBy(
    params => params,
    apiDirList
);
