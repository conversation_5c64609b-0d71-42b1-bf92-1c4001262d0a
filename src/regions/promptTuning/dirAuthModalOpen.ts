import {createRegion} from 'region-react';
import {DirItem} from '@/api/ievalue/promptDir';

interface Props {
    dirItem: DirItem;
    createFinished?: (data: DirItem) => void;
}

const dirAuthModalOpenRegion = createRegion<Props>();

export const useDirAuthModalProps = dirAuthModalOpenRegion.useValue;

export const openDirAuthModalWithProps = dirAuthModalOpenRegion.set;

export const closeDirAuthModal = dirAuthModalOpenRegion.reset;
