import {createRegion} from 'region-react';
import {DirItem} from '@/api/ievalue/promptDir';

interface Props {
    parentDir: DirItem;
    createFinished?: (data: DirItem) => void;
}

const createDirModalOpenRegion = createRegion<Props>();

export const useCreateDirModalProps = createDirModalOpenRegion.useValue;

export const openCreateDirModalWithProps = createDirModalOpenRegion.set;

export const closeCreateDirModal = createDirModalOpenRegion.reset;
