import {createMappedRegion} from 'region-react';
import {DirItemDetail, apiDirInfo} from '@/api/ievalue/promptDir';

const dirInfoRegion = createMappedRegion<number, DirItemDetail>();

export const useDirInfo = dirInfoRegion.useValue;
export const useDirInfoLoading = dirInfoRegion.useLoading;
export const getDirInfoValue = dirInfoRegion.getValue;
export const setDirInfoValue = dirInfoRegion.set;
export const loadDirInfo = dirInfoRegion.loadBy(
    params => params.id,
    apiDirInfo
);
