import {createRegion} from 'region-react';
import {DirItem} from '@/api/ievalue/promptDir';

interface Props {
    refresh?: () => void;
    dirItem: DirItem;
    taskIDs: number[];
}

const taskMoveToModalOpenRegion = createRegion<Props>();

export const useTaskMoveToModalProps = taskMoveToModalOpenRegion.useValue;

export const openTaskMoveToModalWithProps = taskMoveToModalOpenRegion.set;

export const closeTaskMoveToModal = taskMoveToModalOpenRegion.reset;
