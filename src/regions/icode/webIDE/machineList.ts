import {createMappedRegion, createRegion} from 'region-core';
import {
    apiGetMachines,
    ParamsOfGetMachines,
    ResponseMachine,
    apiGetMachineList,
    Machine,
    MachinesWidthPageInfo,
    ShardWorkSpaceByMachine,
    apiGetSharedWorkspacesByMachine,
} from '@/api/icode/webIDE';

const myMachineListParam = createRegion<ParamsOfGetMachines>({ideType: 999, page: 0, pageSize: 20});

export const useMyMachineListParam = myMachineListParam.useValue;

export const resetMyMachineListParam = myMachineListParam.reset;

export const setMyMachineListParam = (params: ParamsOfGetMachines) => {
    myMachineListParam.set(pre => ({...pre, ...params}));
};

const transformMyMachine = (machine: ResponseMachine): Machine => {
    return {
        ...machine,
        name: machine.hostname === machine.ip ? '个人开发机' : machine.hostname,
    };
};

const myMachinesRegion = createRegion<{maclist: ResponseMachine[]}>();

export const loadMyMachines = myMachinesRegion.loadBy(
    () => apiGetMachineList({ideType: 999}) // ideType 999以获取全部开发机
);

export function useMyMachines(): Machine[] {
    const maclist = myMachinesRegion.useValue()?.maclist || [];
    return maclist.map(transformMyMachine);
}

export const useMyMachinesError = myMachinesRegion.useError;

export const useMyMachinesLoading = myMachinesRegion.useLoading;

const myMachineListRegion = createMappedRegion<ParamsOfGetMachines, MachinesWidthPageInfo>();

export const loadMyMachineList = myMachineListRegion.loadBy(params => params, apiGetMachines);

export const useMyMachineListResponse = myMachineListRegion.useValue;

export const useMyMachineList = () => {
    const params = useMyMachineListParam();
    const {mclist = [], totalCount = 0} = useMyMachineListResponse(params) ?? {};
    return {
        machines: mclist.map(item => ({
            ...item,
            vscode: item.vscode.map(transformMyMachine),
            jetbrains: item.jetbrains.map(transformMyMachine),
        })),
        totalCount,
    };
};

export const useMyMachineListLoading = () => {
    const params = useMyMachineListParam();
    const loading = myMachineListRegion.useLoading(params);
    return loading;
};
export const useMyMachineListError = () => {
    const params = useMyMachineListParam();
    const error = myMachineListRegion.useError(params);
    return error;
};

const sharedMachineListRegion = createRegion<ShardWorkSpaceByMachine[]>();

export const loadShardMachineList = sharedMachineListRegion.loadBy(apiGetSharedWorkspacesByMachine);

export const useShardMachineList = sharedMachineListRegion.useValue;

export const ueeShardMachineListLoading = sharedMachineListRegion.useLoading;
