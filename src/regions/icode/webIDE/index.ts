/**
 * @file 代码空间的列表 region
 */
import {identity} from 'lodash';
import {
    useResourceError,
    useResourceListLoading,
    useSharedResourceError,
    useSharedResourceListLoading,
} from './resourceList';
import {useMyMachineListError, useMyMachineListLoading} from './machineList';
import {
    useSharedWorkspacesError,
    useMyWorkspacesError,
    useSharedWorkspacesLoading,
    useMyWorkspacesLoading,
} from './workspaceList';
import {useWelcomePageLoading} from './welcome';

export const useLoading = () => {
    const myMachinesLoading = useMyMachineListLoading();
    const workspaceListLoading = useMyWorkspacesLoading();
    const sharedWorkspacesLoading = useSharedWorkspacesLoading();
    const resourceListLoading = useResourceListLoading();
    const sharedResourceListLoading = useSharedResourceListLoading();
    const welcomePageLoading = useWelcomePageLoading();
    return [
        myMachinesLoading,
        workspaceListLoading,
        sharedWorkspacesLoading,
        resourceListLoading,
        sharedResourceListLoading,
        welcomePageLoading,
    ].some(identity);
};

export const useError = () => {
    const resourceError = useResourceError();
    const sharedResourceError = useSharedResourceError();
    const machineError = useMyMachineListError();
    const workSpaceError = useMyWorkspacesError();
    const sharedWorkspaceError = useSharedWorkspacesError();

    return resourceError ?? machineError ?? workSpaceError ?? sharedWorkspaceError ?? sharedResourceError;
};

export * from './resourceConf';
export * from './resourceList';
export * from './machineList';
export * from './workspaceList';
export * from './viewSettings';
export * from './webPreviewPorts';
export * from './welcome';
