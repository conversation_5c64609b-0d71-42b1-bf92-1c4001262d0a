import {createRegion} from 'region-core';
import {uniq} from 'lodash';
import {useLocalStorage} from '@huse/local-storage';
import {useCallback} from 'react';
import {ConfettiType} from '@/types/common/confetti';

// 用户访问的代码库
const recentVisitRepoNameRegion = createRegion<string[]>([], {withLocalStorageKey: 'ICODE/recentVisitRepoName'});

export const useRecentVisitRepoName = recentVisitRepoNameRegion.useValue;

export const pushRecentVisitRepoName = (repoName: string) => {
    if (repoName) {
        recentVisitRepoNameRegion.set(array => uniq([repoName, ...array]));
    }
};

// 用户访问的上线公告记录
export const recentVisitUpdateInfo = createRegion<number>(0, {withLocalStorageKey: 'ICODE/recentVisitUpdateInfo'});

// icode评审页页面刷新次数
const reviewUnloadRegion = createRegion<string>(
    '',
    {withLocalStorageKey: 'ICODE/reviewUnload'}
);

export const useReviewUnload = reviewUnloadRegion.useValue;

export const setReviewUnload = reviewUnloadRegion.set;

// 快捷键启用状态
const shortKeysEnableRegion = createRegion<boolean>(true, {withLocalStorageKey: 'ICODE/shortKeysEnable'});

export const useShortKeysEnable = shortKeysEnableRegion.useValue;

export const setShortKeysEnable = shortKeysEnableRegion.set;

// icode合入烟花特效的类别
const confettiTypeRegion = createRegion<ConfettiType>('realistic-look', {withLocalStorageKey: 'ICODE/confettiType'});

export const useConfettiType = confettiTypeRegion.useValue;

export const setConfettiType = (type: ConfettiType) => confettiTypeRegion.set(type);

// 评审页查看错误日志的阅读行设置项
const readErrorLogSettingRegion = createRegion<number>(3, {withLocalStorageKey: 'ICODE/reviewErrorLogSetting'});

export const useReadErrorLogSetting = readErrorLogSettingRegion.useValue;

export const setReadErrorLogSetting = readErrorLogSettingRegion.set;

const docsIsReadRegion = createRegion<string[]>(
    [],
    {withLocalStorageKey: 'ICODE/repoDocsIsReadRegion'}
);

export const useDocsIsRead = docsIsReadRegion.useValue;

export const setDocsIsRead = docsIsReadRegion.set;

// 首页代码库列表是否要过滤已冻结的代码库
const repoListFrozenRepoFiltersRegion = createRegion<string[]>(
    [],
    {withLocalStorageKey: 'ICODE/repoListContainFrozenRepo'}
);

export const useRepoListFrozenRepoFilters = repoListFrozenRepoFiltersRegion.useValue;

export const getRepoListFrozenRepoFilters = repoListFrozenRepoFiltersRegion.getValue;

export const setRepoListFrozenRepoFilters = repoListFrozenRepoFiltersRegion.set;

// 给携带注释或者编码规范增加tour的localstorage region
// 首页代码库列表是否要过滤已冻结的代码库
const autoCumCodeTourRegion = createRegion<boolean>(
    true,
    {withLocalStorageKey: 'ICODE/autoCumCodeTour'}
);

export const useAutoCumCodeTour = autoCumCodeTourRegion.useValue;

export const getAutoCumCodeTour = autoCumCodeTourRegion.getValue;

export const setAutoCumCodeTour = autoCumCodeTourRegion.set;

const codeSuggestionAdoptButtonRegion = createRegion<boolean>(
    true,
    {withLocalStorageKey: 'ICODE/codeSuggestionAdoptButtonTour'}
);

export const useCodeSuggestionAdoptButtonTour = codeSuggestionAdoptButtonRegion.useValue;

export const getCodeSuggestionAdoptButtonTour = codeSuggestionAdoptButtonRegion.getValue;

export const setCodeSuggestionAdoptButtonTour = codeSuggestionAdoptButtonRegion.set;

interface DeletedIntelligentPreDraft {
    id: string;
    deleteTime: Date;
}

// 本地删除的智能评审的评论 包括点放弃/采纳
export function useDeletedIntelligentPreDraft() {
    const [storageValue, setValueToStorage] =
        useLocalStorage<DeletedIntelligentPreDraft[]>('ICODE/deletedIntelligentPreDraft', []);

    const addDeletedIntelligentPreDraft = useCallback(
        (id: string) => {
            setValueToStorage([...storageValue, {id, deleteTime: new Date()}]);
        },
        [storageValue, setValueToStorage]
    );

    return {
        deletedIntelligentPreDraft: storageValue,
        addDeletedIntelligentPreDraft,
    };
}

// 是否已阅读在线编辑的双击单元格提示
const hasReadOnlineEditCellTipsRegion = createRegion<boolean>(
    false,
    {withLocalStorageKey: 'COMATESTACK/hasReadOnlineEditCellTips'}
);

export const useHasReadOnlineEditCellTips = hasReadOnlineEditCellTipsRegion.useValue;

export const setHasReadOnlineEditCellTips = hasReadOnlineEditCellTipsRegion.set;
