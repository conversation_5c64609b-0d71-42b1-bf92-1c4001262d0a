import {createMappedRegion} from 'region-react';
import {WorkFlowSubPhase} from '@/types/comatestack/workflow';

type TaskId = number;

const activeSubPhaseRegion = createMappedRegion<0 | TaskId, WorkFlowSubPhase>();

export const useActiveSubPhase = activeSubPhaseRegion.useValue;

export const setActiveSubPhase = activeSubPhaseRegion.set;

export const resetActiveSubPhase = activeSubPhaseRegion.reset;
