import {createMappedRegion} from 'region-react';
import {apiGetTrainingTasks, ParamsGetTrainingTasks} from '@/api/training';
import {TrainingTask} from '@/types/comatestack/training';

const trainingTasksRegion = createMappedRegion<ParamsGetTrainingTasks, TrainingTask[]>([]);

export const getTrainingTasks = trainingTasksRegion.getValue;

export const useTrainingTasks = trainingTasksRegion.useValue;

export const useTrainingTasksLoading = trainingTasksRegion.useLoading;

export const loadTrainingTasks = trainingTasksRegion.loadBy(
    params => params,
    apiGetTrainingTasks
);
