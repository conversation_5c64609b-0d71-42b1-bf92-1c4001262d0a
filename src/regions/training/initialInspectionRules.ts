import {createRegion} from 'region-react';
import {uniqBy} from 'lodash';
import {apiGetAllInspectionRules} from '@/api/training';
import {InspectionRule} from '@/types/comatestack/dataInspection';

interface AllInspectionRules {
    allInspectionTypes: InspectionRule[];
    allInspectionRules: InspectionRule[];
}
export const allInspectionRulesRegion = createRegion<AllInspectionRules>();

export const useAllInspectionRules = allInspectionRulesRegion.useValue;

export const loadAllInspectionRules = allInspectionRulesRegion.loadBy(
    apiGetAllInspectionRules,
    (_, result) => {
        const allInspectionRules = result.map((rule, index) => ({
            ...rule,
            index,
        }));
        const allInspectionTypes = uniqBy(allInspectionRules, 'type');
        return {
            allInspectionTypes,
            allInspectionRules,
        };
    }
);
