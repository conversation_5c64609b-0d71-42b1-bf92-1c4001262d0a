import {createRegion} from 'region-react';
import {apiGetBaseModel} from '@/api/training';
import {ModelTrainingBaseModel} from '@/types/comatestack/trainingModel';

type Classification = 'LLM' | 'SD';

const modelRepoListRegion = createRegion<ModelTrainingBaseModel>();

export const useModelTrainingBaseModelLoading = modelRepoListRegion.useLoading;
export const useModelTrainingBaseModel = modelRepoListRegion.useValue;

export const loadModelTrainingBaseModel = modelRepoListRegion.loadBy(
    apiGetBaseModel
);

export const useClassificationBaseModel = (classification: Classification) => {
    const allModel = useModelTrainingBaseModel();
    if (classification === 'LLM') {
        return allModel?.llmBaseModel;
    }
    return allModel?.sdBaseModel;
};

export const useModelVersions = (classification: Classification, modelName: string) => {
    const baseModel = useClassificationBaseModel(classification);
    return baseModel?.find(v => v.modelName === modelName)?.modelVersions;
};

export const useModelMethodConfigs = (
    classification: Classification,
    modelName: string,
    modelVersion: string
) => {
    const modelVersions = useModelVersions(classification, modelName);
    return modelVersions?.find(v => v.modelVersion === modelVersion)?.methodConfigs;
};

export const useParamConfigs = (
    classification: Classification,
    modelName: string,
    modelVersion: string,
    parameterScale: string
) => {
    const modelMethodConfigs = useModelMethodConfigs(classification, modelName, modelVersion);
    return modelMethodConfigs?.find(v => v.trainMethod === parameterScale)?.configs;
};
