import {createMappedRegion} from 'region-react';
import {apiGet<PERSON>iewHeader, ParamsGetViewHeader, ResultGetViewHeader} from '@/api/training';

const region = createMappedRegion<ParamsGetViewHeader, ResultGetViewHeader>();

export const getFileViewHeader = region.getValue;

export const useFileViewHeader = region.useValue;

export const loadFileViewHeader = region.loadBy(
    params => params,
    apiGetViewHeader
);
