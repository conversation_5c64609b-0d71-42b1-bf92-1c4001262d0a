import {createMappedRegion} from 'region-react';
import {CombinedMappingResult} from '@/types/comatestack/dataInspection';

// taskId
const activeProcessFileRegion = createMappedRegion<number, CombinedMappingResult>();

export const useActiveProcessFile = activeProcessFileRegion.useValue;

export const setActiveProcessFile = activeProcessFileRegion.set;

export const resetActiveProcessFile = activeProcessFileRegion.reset;
