import {createRegion} from 'region-react';
import {DatasetOnboardFile} from '@baidu/devops-components/es/DatasetFileTreeSelect/types';
import {apiGetListFiles} from '@/api/icode';
import {APP_IS_EXTERNAL} from '@/constants/app';

const listFilesRegion = createRegion<DatasetOnboardFile[]>();

const appInternalLoadListFiles = listFilesRegion.loadBy(apiGetListFiles);

const appExternalLoadListFiles = () => {
    listFilesRegion.set([]);
};

export const loadListFiles = APP_IS_EXTERNAL ? appExternalLoadListFiles : appInternalLoadListFiles;

export const useListFiles = listFilesRegion.useValue;
