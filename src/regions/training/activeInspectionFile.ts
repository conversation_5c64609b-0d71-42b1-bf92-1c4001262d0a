import {createMappedRegion} from 'region-react';
import {CombinedMappingResult, InspectionFileResult} from '@/types/comatestack/dataInspection';
import {apiGetInspectionFileResult} from '@/api/training';

// taskId
const activeInspectionFileRegion = createMappedRegion<number, CombinedMappingResult>();

export const useActiveInspectionFile = activeInspectionFileRegion.useValue;

export const setActiveInspectionFile = activeInspectionFileRegion.set;

export const resetActiveInspectionFile = activeInspectionFileRegion.reset;

interface KeyFileResult {
    taskId: number;
    filePath: string;
}

const inspectionFileResultRegion = createMappedRegion<KeyFileResult, InspectionFileResult>();

export const useInspectionFileResult = inspectionFileResultRegion.useValue;

export const loadInspectionFileResult = inspectionFileResultRegion.loadBy(
    ({taskId, filePath}) => ({taskId, filePath}),
    apiGetInspectionFileResult
);
