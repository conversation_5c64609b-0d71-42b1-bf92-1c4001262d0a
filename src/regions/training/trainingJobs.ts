import {createMappedRegion} from 'region-react';
import {apiGetModelTrainingJobList, apiSearchModelTrainingList} from '@/api/training';
import {TrainingJob} from '@/types/comatestack/training';

// datasetName
const trainingJobsRegion = createMappedRegion<string, TrainingJob[]>([]);

export const useTrainingJobs = trainingJobsRegion.useValue;

export const useTrainingJobsLoading = trainingJobsRegion.useLoading;

export const loadTrainingJobs = trainingJobsRegion.loadBy(
    params => params.datasetName,
    apiGetModelTrainingJobList
);

export const loadTrainingTasksAndJobs = trainingJobsRegion.loadBy(
    params => params.datasetName,
    apiSearchModelTrainingList
);


