import {createMappedRegion} from 'region-react';
import {apiGetTrainingTask, ParamsGetTrainingTask} from '@/api/training';
import {TrainingTask} from '@/types/comatestack/training';

const trainingTaskRegion = createMappedRegion<ParamsGetTrainingTask, TrainingTask>();

export const getTrainingTask = trainingTaskRegion.getValue;

export const useTrainingTask = trainingTaskRegion.useValue;

export const useTrainingTaskLoading = trainingTaskRegion.useLoading;

export const loadTrainingTask = trainingTaskRegion.loadBy(
    params => params,
    apiGetTrainingTask,
    (_, result) => {
        if (result.dataInspectionConfig.inspectionRules) {
            result.dataInspectionConfig.inspectionRules.forEach((item, index) => {
                item.index = index;
            });
        }
        return result;
    }
);
