import {createMappedRegion} from 'region-react';
import {ResultFuncInitialized} from 'region-core/es/types';
import {Annotation, AnnotationResult} from '@/types/label/annotation';
import {resetFuture, resetPast, takeSnapshot} from '@/regions/annotation/timeMachine';

// annotation.id 是数字，> 0 为远程保存的，< 0 为草稿
const annotationCacheRegion = createMappedRegion<number, Annotation>();

export const getAnnotationCache = annotationCacheRegion.getValue;

export const useAnnotationCache = annotationCacheRegion.useValue;

export const setAnnotationCache = annotationCacheRegion.set;

const resetAnnotationCache = annotationCacheRegion.reset;

export const resetAnnotations = (key: number) => {
    resetAnnotationCache(key);
    resetPast(key);
    resetFuture(key);
};

// eslint-disable-next-line max-len
export const setAnnotationWithTimeMachineSnapshot = (key: number, annotation: Annotation | ResultFuncInitialized<Annotation>) => {
    const snapshot = getAnnotationCache(key);
    if (snapshot) {
        takeSnapshot(key, snapshot);
    }
    setAnnotationCache(key, annotation);
};

interface AddAnnotationResultParams {
    annotationResult: AnnotationResult;
    keepOne: boolean;
}

export const addAnnotationResult = (id: number, params: AddAnnotationResultParams) => {
    const {annotationResult, keepOne} = params;
    setAnnotationWithTimeMachineSnapshot(id, prevValue => {
        const prevResult = prevValue?.result ?? [];
        if (keepOne) {
            const filtered = prevResult.filter(
                item => {
                    const shouldDelete = item.type === annotationResult.type
                            && item.from_name === annotationResult.from_name;
                    return !shouldDelete;
                }
            );
            return {...prevValue, result: [...filtered, annotationResult]};
        }
        return {...prevValue, result: [...prevResult, annotationResult]};
    });
};

export const deleteAnnotationResult = (id: number, annotationResultId: string) => {
    setAnnotationWithTimeMachineSnapshot(id, annotation => {
        const nextResult = annotation?.result?.filter(value => value.id !== annotationResultId);
        return ({
            ...annotation,
            result: nextResult,
        });
    });
};

export const updateAnnotationResult = (
    id: number,
    annotationResultId: string,
    newAnnotationResult: AnnotationResult
) => {
    setAnnotationWithTimeMachineSnapshot(id, annotation => {
        if (!annotation?.result) {
            return annotation;
        }
        const newResult = annotation.result.map(value => {
            if (value.id === annotationResultId) {
                return newAnnotationResult;
            }
            return value;
        });
        return ({
            ...annotation,
            result: newResult,
        });
    });
};
