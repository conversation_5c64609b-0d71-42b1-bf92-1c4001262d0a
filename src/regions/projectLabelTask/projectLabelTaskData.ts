import {createMappedRegion} from 'region-react';
import {LabelStudioDataSourceParams, apiGetDataSource, LabelTaskTableData} from '@/api/labelStudio';
import {useLabelParams} from '@/hooks/label/useLabelParams';
import {usePagination} from '@/hooks/common/pageSearchParams';
import {useCurrentViewId} from './dmViews';

const labelTaskTableDataRegion = createMappedRegion<LabelStudioDataSourceParams, LabelTaskTableData>();

const useLabelTaskTableData = labelTaskTableDataRegion.useValue;

export const useCurrentLabelTaskTableData = () => {
    const viewId = useCurrentViewId();
    const {labelStudioProjectId} = useLabelParams();
    const {page, pageSize} = usePagination();
    const data = useLabelTaskTableData({
        page: page,
        page_size: pageSize,
        view: viewId,
        project: labelStudioProjectId,
    });
    return data;
};

const useLabelTaskTableDataLoading = labelTaskTableDataRegion.useLoading;

export const useCurrentLabelTaskTableDataLoading = () => {
    const viewId = useCurrentViewId();
    const {labelStudioProjectId} = useLabelParams();
    const {page, pageSize} = usePagination();
    const loading = useLabelTaskTableDataLoading({
        page: page,
        page_size: pageSize,
        view: viewId,
        project: labelStudioProjectId,
    });
    return loading;
};

export const loadLabelTaskTableData = labelTaskTableDataRegion.loadBy(
    params => params,
    apiGetDataSource
);
