import {createMappedRegion} from 'region-react';
import {apiGetDmView} from '@/api/labelStudio';
import {DmView} from '@/types/label/project';
import {useLabelParams} from '@/hooks/label/useLabelParams';

// labelStudioProjectId
const dmViewsRegion = createMappedRegion<number, DmView[]>(null);

export const getDmViews = dmViewsRegion.getValue;

const useDmViews = dmViewsRegion.useValue;

export const useCurrentView = () => {
    const {labelStudioProjectId} = useLabelParams();
    const dmViews = useDmViews(labelStudioProjectId);
    return dmViews?.[0];
};

export const useCurrentViewId = () => {
    const currentView = useCurrentView();
    return currentView?.id;
};

export const loadDmViews = dmViewsRegion.loadBy(
    params => params.project,
    apiGetDmView
);


