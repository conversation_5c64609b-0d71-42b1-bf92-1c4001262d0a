import {createMappedRegion} from 'region-react';
import {apiGetImportStatus} from '@/api/labelTask';
import {ParamsPostImportStatus, ResultPostImportStatus} from '@/types/label/labelProject';

const labelProjectImportStatusRegion = createMappedRegion<ParamsPostImportStatus, ResultPostImportStatus>(
    {status: 'INITIALIZED', percent: 0}
);

export const useLabelProjectImportStatus = labelProjectImportStatusRegion.useValue;

export const loadLabelProjectImportStatus = labelProjectImportStatusRegion.loadBy(
    params => params,
    apiGetImportStatus
);
