import {createRegion} from 'region-react';
import {tempTasks} from './tempContants';

export interface Task {
    id: number;
    drafts: any[];
    annotators: number[];
    inner_id: number;
    cancelled_annotations: number;
    total_annotations: number;
    total_predictions: number;
    completed_at: string; // 日期字符串
    annotations_results: string;
    predictions_results: string;
    file_upload: null | any;
    storage_filename: null | string;
    annotations_ids: string;
    review_result: string;
    predictions_model_versions: string;
    updated_by: Array<{
      user_id: number;
    }>;
    data: {
      text: string;
    };
    meta: Record<string, any>;
    created_at: string; // 日期字符串
    updated_at: string; // 日期字符串
    is_labeled: boolean;
    overlap: number;
    comment_count: number;
    unresolved_comment_count: number;
    last_comment_updated_at: null | string;
    project: number;
    comment_authors: any[];
  }

const tasksRegion = createRegion<Task[]>(tempTasks.tasks);

export const useTasks = tasksRegion.useValue;

export const getTasks = tasksRegion.getValue;

export const setTasks = tasksRegion.set;
