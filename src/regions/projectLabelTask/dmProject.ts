import {createMappedRegion} from 'region-react';
import {apiGetDmProject} from '@/api/labelStudio';
import {DmProject} from '@/types/label/project';

// labelStudioProjectId
const dmProjectRegion = createMappedRegion<number, DmProject>(null);

export const useDmProject = dmProjectRegion.useValue;

export const loadDmProject = dmProjectRegion.loadBy(
    params => params.project,
    apiGetDmProject
);

