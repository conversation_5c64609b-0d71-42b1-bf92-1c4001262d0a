import {createMappedRegion} from 'region-react';
import {LabelTaskDetail} from '@/types/label/labelTaskData';
import {apiGetLabelTaskDetail, ParamsLabelTask} from '@/api/labelStudio';
import {useLabelParams} from '@/hooks/label/useLabelParams';
import {Annotation} from '@/types/label/annotation';
import {useLabelTaskId} from '@/hooks/label/useLabelTaskId';
import {createInitialAnnotation} from '@/utils/label/createInitialAnnotation';
import {setCurrentAnnotationId} from '@/regions/annotation/currentAnnotationId';
import {setAnnotationCache} from './annotationCache';

const labelTaskDetailRegion = createMappedRegion<ParamsLabelTask, LabelTaskDetail>();

export const getLabelTaskDetail = labelTaskDetailRegion.getValue;

export const useLabelTaskDetail = labelTaskDetailRegion.useValue;

const setLabelTaskDetail = labelTaskDetailRegion.set;

export const useCurrentLabelTaskDetail = () => {
    const {labelStudioProjectId} = useLabelParams();
    const taskId = useLabelTaskId();
    const labelTaskDetail = useLabelTaskDetail({taskId: taskId, project: labelStudioProjectId});
    return labelTaskDetail;
};

export const addAnnotationToLabelTaskDetail = (params: ParamsLabelTask, annotation: Annotation) => {
    setLabelTaskDetail(params, labelTaskDetail => {
        const nextLabelTaskDetail = {
            ...labelTaskDetail,
            annotations: [annotation, ...labelTaskDetail?.annotations],
        };
        return nextLabelTaskDetail;
    });
};

export const loadLabelTaskDetail = labelTaskDetailRegion.loadBy(
    params => params,
    apiGetLabelTaskDetail
);

interface ParamsAddLabelAnnotationDraft {
    taskId: number;
    labelStudioProjectId: number;
}

export const addLabelAnnotationDraft = (params: ParamsAddLabelAnnotationDraft) => {
    const {labelStudioProjectId, taskId} = params;
    const newAnnotation = createInitialAnnotation(params);

    addAnnotationToLabelTaskDetail({taskId, project: labelStudioProjectId}, newAnnotation);
    setCurrentAnnotationId(taskId, newAnnotation.id);
    setAnnotationCache(newAnnotation.id, newAnnotation);
};
