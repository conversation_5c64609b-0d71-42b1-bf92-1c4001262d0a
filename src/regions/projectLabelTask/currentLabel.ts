import {createRegion} from 'region-react';

interface CurrentLabel {
    name: string;
    toName: string;
    value: string;
    label?: string;
    background?: string;
}

const currentLabelRegion = createRegion<CurrentLabel>();

export const useCurrentLabel = currentLabelRegion.useValue;

export const setCurrentLabel = currentLabelRegion.set;

export const resetCurrentLabel = currentLabelRegion.reset;
