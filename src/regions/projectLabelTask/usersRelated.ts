import {createMappedRegion} from 'region-react';
import {apiGetUsersRelated} from '@/api/labelStudio';
import {UserRelated} from '@/types/label/labelTask';

// labelStudioProjectId
const usersRelatedRegion = createMappedRegion<number, UserRelated[]>();

export const useUsersRelated = usersRelatedRegion.useValue;

export const loadUsersRelated = usersRelatedRegion.loadBy(
    params => params.project,
    apiGetUsersRelated
);
