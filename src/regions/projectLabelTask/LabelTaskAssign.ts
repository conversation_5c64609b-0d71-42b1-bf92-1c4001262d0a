import {createMappedRegion, createRegion} from 'region-react';
import {
    AssignedTaskDetail,
    LabelAssignCount,
    ParamsGetLabelAssignCount,
} from '@/types/label/labelTaskReview';
import {apiGetLabelAssignCount, apiGetAssignedTaskDetail, apiGetReviewTaskGroups} from '@/api/labelTaskAssign';
import {TaskExecutors} from '@/types/label/labelTaskReview';
import {apiGetReviewTaskExecutors} from '@/api/labelTaskAssign';

const assignedTaskDetailRegion = createMappedRegion<{labelStudioProjectId: number}, AssignedTaskDetail>();

export const useAssignedTaskDetail = assignedTaskDetailRegion.useValue;

export const loadAssignedTaskDetail = assignedTaskDetailRegion.loadBy(
    params => params,
    apiGetAssignedTaskDetail
);


const labelAssignCountRegion = createMappedRegion<ParamsGetLabelAssignCount, LabelAssignCount>();

export const useLabelAssignCount = labelAssignCountRegion.useValue;

export const loadLabelAssignCount = labelAssignCountRegion.loadBy(
    params => params,
    apiGetLabelAssignCount
);


const isOpenAssignedTaskModalRegion = createRegion<boolean>(false);

export const useIsOpenAssignedTaskModal = isOpenAssignedTaskModalRegion.useValue;

export const setIsOpenAssignedTaskModal = isOpenAssignedTaskModalRegion.set;


const unassignedCountRegion = createRegion<number>(0);

export const useUnassignedCount = unassignedCountRegion.useValue;

export const setUnassignedCount = unassignedCountRegion.set;


const taskExecutorsRegion = createMappedRegion<{labelStudioProjectId: number}, TaskExecutors>();

export const useTaskExecutors = taskExecutorsRegion.useValue;

export const loadTaskExecutors = taskExecutorsRegion.loadBy(
    params => params,
    apiGetReviewTaskExecutors
);


const taskGroupsRegion = createMappedRegion<{labelStudioProjectId: number}, number>();

export const useTaskGroups = taskGroupsRegion.useValue;

export const loadTaskGroups = taskGroupsRegion.loadBy(
    params => params,
    apiGetReviewTaskGroups
);


const isLockAssignRegion = createRegion<boolean>(false);

export const useIsLockAssign = isLockAssignRegion.useValue;

export const setIsLockAssign = isLockAssignRegion.set;

