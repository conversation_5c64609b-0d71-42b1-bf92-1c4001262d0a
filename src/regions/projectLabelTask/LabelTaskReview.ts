import {createMappedRegion, createRegion} from 'region-react';
import {
    ParamsGetReviewResultItem,
    ReviewResultItem,
} from '@/types/label/labelTaskReview';
import {apiGetReviewResultItem} from '@/api/labelTaskAssign';

const reviewResultItemRegion = createMappedRegion<ParamsGetReviewResultItem, ReviewResultItem>();

export const useReviewResultItem = reviewResultItemRegion.useValue;

export const loadReviewResultItem = reviewResultItemRegion.loadBy(
    params => params,
    apiGetReviewResultItem
);

// 未标注草稿状态（FORBIDDEN_REVIEW）、已经审核（PASS\FAILED\CLEAR）、未审核（UNREVIEWED）
const labelTaskReviewStatusRegion = createRegion<string>(null);

export const useLabelTaskReviewStatus = labelTaskReviewStatusRegion.useValue;

export const setLabelTaskReviewStatus = labelTaskReviewStatusRegion.set;


const labelTaskIsCheckedRegion = createRegion<boolean>(false);

export const useLabelTaskIsChecked = labelTaskIsCheckedRegion.useValue;

export const setLabelTaskIsChecked = labelTaskIsCheckedRegion.set;


// labelStudioProjectId
const isShowReviewTagRegion = createMappedRegion<number, boolean>(false);

export const useIsShowReviewTag = isShowReviewTagRegion.useValue;

export const setIsShowReviewTag = isShowReviewTagRegion.set;
