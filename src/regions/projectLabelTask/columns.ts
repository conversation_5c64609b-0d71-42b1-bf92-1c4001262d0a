import {createMappedRegion} from 'region-react';
import {apiGetColumn} from '@/api/labelStudio';
import {LabelColumn} from '@/types/label/project';
import {getLabelColumnShouldHide} from '@/utils/label/columns';

// labelStudioProjectId
const columnsRegion = createMappedRegion<number, LabelColumn[]>();

export const useColumns = columnsRegion.useValue;

export const loadColumns = columnsRegion.loadBy(
    params => params.project,
    apiGetColumn,
    (_, result) => {
        return result.columns.filter(item => {
            const shouldHide = getLabelColumnShouldHide(item);
            return !shouldHide;
        });
    }
);

// labelStudioProjectId
const hiddenColumnsIdRegion = createMappedRegion<number, string[]>([]);

export const useHiddenColumnsId = hiddenColumnsIdRegion.useValue;

export const setHiddenColumnsId = hiddenColumnsIdRegion.set;
