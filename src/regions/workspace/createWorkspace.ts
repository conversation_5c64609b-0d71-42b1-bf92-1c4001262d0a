import {createRegion} from 'region-react';

const createWorkspaceModalRegion = createRegion(false);

export const useCreateWorkspaceModalOpen = createWorkspaceModalRegion.useValue;

export const openCreateWorkspaceModal = () => createWorkspaceModalRegion.set(true);

export const closeCreateWorkspaceModal = () => createWorkspaceModalRegion.set(false);


const newWorkspaceUuidRegion = createRegion('');

export const useNewWorkspaceUuid = newWorkspaceUuidRegion.useValue;

export const setNewWorkspaceUuid = (workspaceUuid: string) => newWorkspaceUuidRegion.set(workspaceUuid);
