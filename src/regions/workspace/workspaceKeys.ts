import {createMappedRegion} from 'region-react';
import {WorkspaceKey} from '@/types/comatestack/workspace';
import {apiGetWorkspaceKeys} from '@/api/workspace';

const workspaceKeysRegion = createMappedRegion<string, WorkspaceKey[]>([]);

export const useWorkspaceKeys = workspaceKeysRegion.useValue;

export const loadWorkspaceKeys = workspaceKeysRegion.loadBy(
    params => params.workspaceUuid,
    apiGetWorkspaceKeys
);
