import {createMappedRegion} from 'region-react';
import {Workspace} from '@/types/comatestack/workspace';
import {apiGetWorkspace} from '@/api/workspace';
import {useWorkspaceUuidByPath} from '@/hooks/current/routeParams';

const workspaceRegion = createMappedRegion<string, Workspace>();

export const useWorkspace = workspaceRegion.useValue;

export const useWorkspaceError = workspaceRegion.useError;

export const useCurrentWorkspace = () => {
    const workspaceUuid = useWorkspaceUuidByPath();
    return useWorkspace(workspaceUuid);
};

export const loadWorkspace = workspaceRegion.loadBy(
    params => params.workspaceUuid,
    apiGetWorkspace
);
