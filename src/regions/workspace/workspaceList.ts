import {createRegion} from 'region-react';
import {Workspace} from '@/types/comatestack/workspace';
import {apiGetWorkspaceList} from '@/api/workspace';
import {favoriteSorter} from '@/utils/sorter';
import {APP_IS_EXTERNAL} from '@/constants/app';

const workspaceListRegion = createRegion<Workspace[]>([]);

export const useWorkspaceListLoading = workspaceListRegion.useLoading;
export const useWorkspaceList = workspaceListRegion.useValue;

const appInternalLoadWorkspaceList = workspaceListRegion.loadBy(
    apiGetWorkspaceList,
    (_, result) => {
        result.sort(favoriteSorter);
        return result;
    }
);

const appExternalLoadWorkspaceList = () => {
    workspaceListRegion.set([]);
};

export const loadWorkspaceList = APP_IS_EXTERNAL ? appExternalLoadWorkspaceList : appInternalLoadWorkspaceList;
