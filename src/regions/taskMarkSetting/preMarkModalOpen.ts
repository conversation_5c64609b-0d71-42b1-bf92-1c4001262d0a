import {createRegion} from 'region-react';

type OpenStatus = 'add' | 'edit' | 'close';

const preMarkModalOpenRegion = createRegion<OpenStatus>('close');

const preMarkModalRuleIdRegion = createRegion<number>();

export const usePreMarkModalOpen = preMarkModalOpenRegion.useValue;

export const usePreMarkRuleId = preMarkModalRuleIdRegion.useValue;

export const openAddPreMarkRule = () => {
    preMarkModalOpenRegion.set('add');
};

export const openEditPreMarkRule = (ruleId: number) => {
    preMarkModalOpenRegion.set('edit');
    preMarkModalRuleIdRegion.set(ruleId);
};

export const closePreMark = () => {
    preMarkModalOpenRegion.set('close');
    preMarkModalRuleIdRegion.reset();
};

