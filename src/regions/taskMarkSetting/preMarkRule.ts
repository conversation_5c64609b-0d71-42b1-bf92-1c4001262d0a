import {createMappedRegion} from 'region-react';
import {apiGetRuleDetail} from '@/api/modelMark';
import {ParamsRuleBase, RuleDetail} from '@/types/comatestack/modelMark';

const preMarkRuleRegion = createMappedRegion<ParamsRuleBase, RuleDetail>();

export const getPreMarkRule = preMarkRuleRegion.getValue;

export const usePreMarkRule = preMarkRuleRegion.useValue;

export const loadPreMarkRule = preMarkRuleRegion.loadBy(
    params => params,
    apiGetRuleDetail
);
