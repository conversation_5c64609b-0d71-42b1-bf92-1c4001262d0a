import {createMappedRegion} from 'region-react';
import {TaskMarkDetail} from '@/types/comatestack/taskMarkSetting';
import {apiGetTaskSettingDetail} from '@/api/taskMarkSetting';

const taskSettingDetailRegion = createMappedRegion<number, TaskMarkDetail>();

export const useTaskSettingDetail = taskSettingDetailRegion.useValue;

export const useTaskSettingDetailLoading = taskSettingDetailRegion.useLoading;

export const loadTaskSettingDetail = taskSettingDetailRegion.loadBy(
    params => params.labelProjectId,
    apiGetTaskSettingDetail
);
