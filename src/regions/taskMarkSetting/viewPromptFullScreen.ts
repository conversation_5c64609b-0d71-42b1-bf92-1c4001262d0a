import {createRegion} from 'region-react';
import {ModelPromptDetail} from '@/types/ievalue/prompt';

const viewPromptFullScreenRegion = createRegion<{record: ModelPromptDetail, mode: 'EDIT' | 'CREATE'}>();

export const useViewPromptFullScreen = viewPromptFullScreenRegion.useValue;

export const setViewPromptFullScreen = viewPromptFullScreenRegion.set;

export const resetViewPromptFullScreen = viewPromptFullScreenRegion.reset;
