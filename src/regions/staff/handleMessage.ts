import {last, uniq} from 'lodash';
import {Chat, ChatMessage} from '@/types/staff/chat';
import {DEFAULT_TASK_ID} from '@/constants/staff';
import {getCurrentChatId, getSdkMessageListener} from './chatSdk';
import {getCurrentChat, getTask, setChat, setMessage, setStage, setTask} from './chat';

// eslint-disable-next-line complexity
const messageReducer = (chat: Chat, message: ChatMessage) => {
    const {messageIds = [], stageIds = {}, taskIds = []} = chat;
    const messageTaskIds = (message.tasks ?? []).map(task => task.id);

    // 这里判断逻辑根本不知道为什么
    const hasNewMeta = message?.meta?.title
        // 下边两个场景是为了应对meta变更的影响，主要是因为不知道为什么上边会是那个逻辑
        || message?.meta?.disabled !== chat?.meta?.disabled
        || message?.meta?.cancelDisabled !== chat?.meta?.cancelDisabled;

    const hasNewMessage = !messageIds.includes(message.messageId);

    let hasNewStage = false;
    const nextStageIds = (message.stages ?? []).reduce(
        (acc, stage) => {
            const taskId = stage.taskId || DEFAULT_TASK_ID;
            if (!acc[taskId]) {
                acc[taskId] = [];
            }
            if (!acc[taskId].includes(stage.id)) {
                hasNewStage = true;
                acc[taskId].push(stage.id);
            }
            return acc;
        },
        stageIds
    );

    if (!hasNewMeta && !hasNewMessage && !hasNewStage && (!chat.running && message.finish)) {
        return chat;
    }

    const mergedTaskIds = uniq([...taskIds, ...messageTaskIds]);
    const hasNewTask = messageTaskIds.some(taskId => !taskIds.includes(taskId));

    // 对于旧的数据，如果任务id数组是空的，但是有stageId，那么赋予其默认的任务id
    const hasStages = !!message.stages?.length;
    const nextTaskIds = (!mergedTaskIds.length && hasStages)
        ? [DEFAULT_TASK_ID]
        : mergedTaskIds;
    const nextCurrentTaskId = (!mergedTaskIds.length && hasStages)
        ? DEFAULT_TASK_ID
        : hasNewTask ? last(nextTaskIds) : chat.currentTaskId;
    return {
        ...chat,
        running: (message.role === 'USER' && !['callback', 'agent'].includes(message.source))
            || (message.role === 'AGENT' && !message.finish),
        meta: hasNewMeta ? message.meta : chat.meta,
        messageIds: hasNewMessage ? [...messageIds, message.messageId] : messageIds,
        taskIds: nextTaskIds,
        currentTaskId: nextCurrentTaskId,
        stageIds: nextStageIds,
        currentStageId: hasNewStage ? last(nextStageIds?.[nextCurrentTaskId]) : chat.currentStageId,
    };

};

// 每次 sse 到达的时候触发这个函数
export const messageArrived = async (message: ChatMessage) => {
    setMessage(message.messageId, message);
    message?.stages?.forEach(stage => {
        // 先更新 stage
        const taskId = stage.taskId ?? DEFAULT_TASK_ID;
        setStage(taskId, stage.id, {...stage, messageId: message.messageId, taskId});
    });

    // 多任务更新tasks
    message?.tasks?.forEach(task => {
        setTask(task.id, {...task, messageId: message.messageId});
    });
    // 单任务更新tasks
    if (!message?.tasks?.length && message?.stages?.length) {
        const defaultTask = getTask(DEFAULT_TASK_ID);
        setTask(DEFAULT_TASK_ID, {
            id: DEFAULT_TASK_ID,
            name: message?.meta?.title || defaultTask?.name || '默认任务',
            status: message?.meta?.status || defaultTask?.status,
            statusText: message?.meta?.statusText || defaultTask?.statusText,
            messageId: message.messageId,
        });
    }

    const conversationId = getCurrentChatId();
    await new Promise(resolve => setTimeout(resolve, 200));
    setChat(conversationId, chat => messageReducer(chat, message));
    const listeners = getSdkMessageListener(conversationId);
    if (listeners?.length) {
        listeners.forEach(listener => listener(message));
    }
};

export const handleRemoveMessage = (messageId: string) => {
    const currentChat = getCurrentChat();
    const {conversationId} = currentChat;

    setChat(conversationId, item => {
        const {messageIds = []} = currentChat;
        const newMessageIds = messageIds.filter(id => id !== messageId);
        return ({
            ...item,
            messageIds: newMessageIds,
        });
    });
};
