// import {createMappedRegion} from 'region-react';
import {APP_WEBSOCKET_PREFIX} from '@/constants/app';
import {createWebsocket} from '@/utils/staff/createWebsocket';
import {messageArrived} from './handleMessage';
import {notificationArrived} from './notification';

// 或许未来可能需要把 websocket 缓存，但是目前暂时还不需要
// key 是 chatId
// const chatWsRegion = createMappedRegion<string, WebSocket>();
//
// export const getChatWs = chatWsRegion.getValue;
//
// export const setChatWs = chatWsRegion.set;

export const createChatWebsocket = (chatId: string) => {
    const url = `${APP_WEBSOCKET_PREFIX}/api/comatestack/agentic-infra/ws/chat?conversationId=${chatId}`;
    const ws = createWebsocket(url, messageArrived);
    // setChatWs(chatId, ws);
    return ws;
};

// 或许未来可能需要把 websocket 缓存，但是目前暂时还不需要
// key 是 app
// const notificationWsRegion = createMappedRegion<string, WebSocket>();
//
// export const getNotificationWs = notificationWsRegion.getValue;
//
// export const setNotificationWs = notificationWsRegion.set;

export const createNotificationWebsocket = (app: string) => {
    const url = `${APP_WEBSOCKET_PREFIX}/api/comatestack/agentic-infra/ws/common?app=${app}&channel=bubble`;
    const ws = createWebsocket(url, notificationArrived);
    // setNotificationWs(app, ws);
    return ws;
};
