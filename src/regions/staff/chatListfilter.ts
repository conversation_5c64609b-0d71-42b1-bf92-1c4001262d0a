import {createRegion} from 'region-react';
import {ChatListFilterParam} from '@/api/staff';

const chatListfilterDataRegion = createRegion<ChatListFilterParam>({});

export const useChatListFilterData = chatListfilterDataRegion.useValue;

export const appendChatListFilterData = (value: any) =>
    chatListfilterDataRegion.set(v => ({...v, ...value}));

export const setChatListFilterData = chatListfilterDataRegion.set;

export const resetChatListFilterData = chatListfilterDataRegion.reset;
