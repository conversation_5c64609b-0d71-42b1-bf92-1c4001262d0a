import {createRegion, createMappedRegion} from 'region-react';

const currentChatIdRegion = createRegion<string>();

export const getCurrentChatId = currentChatIdRegion.getValue;

export const useCurrentChatId = currentChatIdRegion.useValue;

export const setCurrentChatId = currentChatIdRegion.set;

const currentChatOpenRegion = createRegion<boolean>();

export const useCurrentChatOpen = currentChatOpenRegion.useValue;

export const setCurrentChatOpen = currentChatOpenRegion.set;

const currentNotificationAppRegion = createRegion<string>();

export const getCurrentNotificationApp = currentNotificationAppRegion.getValue;

export const useCurrentNotificationApp = currentNotificationAppRegion.useValue;

export const setCurrentNotificationApp = currentNotificationAppRegion.set;

type Listener = (data: any) => void;

// key 是 chatId
const sdkMessageListenerRegion = createMappedRegion<string, Listener[]>([]);

export const getSdkMessageListener = sdkMessageListenerRegion.getValue;

export const setSdkMessageListener = sdkMessageListenerRegion.set;

// key 是 app
const sdkNotificationListenerRegion = createMappedRegion<string, Listener[]>([]);

export const getSdkNotificationListener = sdkNotificationListenerRegion.getValue;

export const setSdkNotificationListener = sdkNotificationListenerRegion.set;
