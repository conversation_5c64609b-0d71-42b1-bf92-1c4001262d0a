import {createRegion} from 'region-react';
import {uniqueId} from 'lodash';
import {Notification} from '@/types/staff/notification';
import {getCurrentNotificationApp, getSdkMessageListener} from './chatSdk';

const NotificationsRegion = createRegion<Notification[]>([]);

export const getNotifications = NotificationsRegion.getValue;

export const useNotifications = NotificationsRegion.useValue;

export const setNotifications = NotificationsRegion.set;

export const notificationArrived = (notification: Notification) => {
    setNotifications(prev => {
        if (notification?.displayTypes?.includes('notification')) {
            return [...prev, {...notification, id: uniqueId()}];
        }
        return prev;
    });
    if (notification?.displayTypes?.includes('app')) {
        const app = getCurrentNotificationApp();
        const listeners = getSdkMessageListener(app);
        if (listeners?.length) {
            listeners.forEach(listener => listener(notification));
        }
    }
};

export const removeNotificationById = (id: string) => {
    setNotifications(prev => {
        return prev.filter(notification => notification.id !== id);
    });
};
