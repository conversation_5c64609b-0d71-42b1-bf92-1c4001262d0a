import {createRegion} from 'region-react';

const supportAreaFullRegion = createRegion<boolean>(false);

export const useSupportAreaFull = supportAreaFullRegion.useValue;

export const setSupportAreaFull = () => supportAreaFullRegion.set(true);

export const toggleSupportAreaFull = () => supportAreaFullRegion.set(x => !x);

const supportAreaCollapseRegion = createRegion<boolean>(false);

export const useSupportAreaCollapse = supportAreaCollapseRegion.useValue;

export const setSupportAreaCollapse = () => supportAreaCollapseRegion.set(true);

export const toggleSupportAreaCollapse = () => supportAreaCollapseRegion.set(x => !x);
