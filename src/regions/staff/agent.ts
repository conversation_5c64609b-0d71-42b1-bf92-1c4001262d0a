import {createMappedRegion, createRegion} from 'region-react';
import {Agent} from '@/types/staff/chat';
import {apiGetAgentList} from '@/api/staff';
import {
    getCurrentNotificationApp,
    useCurrentNotificationApp,
} from './chatSdk';

const agentListRegion = createMappedRegion<string, Agent[]>([]);

export const useAgentListLoading = agentListRegion.useLoading;

const getAgentListFn = agentListRegion.getValue;

const useAgentListFn = agentListRegion.useValue;

const loadAgentListFn = agentListRegion.loadBy(
    params => params?.app,
    apiGetAgentList
);

export const getAgentList = () => {
    const app = getCurrentNotificationApp();
    return getAgentListFn(app);
};

export const useAgentList = () => {
    const app = useCurrentNotificationApp();
    return useAgentListFn(app);
};

export const loadAgentList = () => {
    const app = getCurrentNotificationApp();
    return loadAgentListFn({app});
};

const currentAgentIdRegion = createRegion<number>();

export const getCurrentAgentId = currentAgentIdRegion.getValue;

export const setCurrentAgentId = currentAgentIdRegion.set;

export const useCurrentAgentId = currentAgentIdRegion.useValue;

export const useCurrentAgent = () => {
    const agentList = useAgentList();
    const currentAgentId = useCurrentAgentId();
    return agentList?.find(agent => agent.id === currentAgentId) || {} as Agent;
};

