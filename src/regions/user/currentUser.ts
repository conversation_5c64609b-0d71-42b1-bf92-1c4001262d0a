import {createRegion} from 'region-react';
import {CurrentUser} from '@/types/comatestack/suggestUsers';
import {apiGetCurrentUser, apiGetExternalUser} from '@/api/base';
import {APP_IS_EXTERNAL} from '@/constants/app';


const initialUser: CurrentUser = {
    username: window?.__icloud__?.username ?? '',
} as CurrentUser;

const currentUserRegion = createRegion<CurrentUser>(initialUser);

export const loadCurrentUser = currentUserRegion.loadBy(
    APP_IS_EXTERNAL ? apiGetExternalUser : apiGetCurrentUser,
    (_, result) => ({...result, username: APP_IS_EXTERNAL ? result?.stackDisplayName : result.userName})
);

export const getCurrentUser = currentUserRegion.getValue;

export const useCurrentUser = currentUserRegion.useValue;

export const getCurrentUsername = () => {
    const currentUser = getCurrentUser();
    return currentUser?.username;
};

export const useCurrentUsername = (): string => {
    const currentUser = useCurrentUser();
    return currentUser?.username;
};
