import {createMappedRegion} from 'region-react';
import {ParamsBatchQueryUsers, ParamsGetUserGroup, ParamsUserGroupId} from '@/types/params/userGroup';
import {apiGetUserGroups, apiPostBatchQueryUsers} from '@/api/userGroup';
import {UserDetail, UserGroup} from '@/types/comatestack/userGroup';
import {apiGetBoundProjects} from '@/api/userGroup';
import {Project} from '@/types/comatestack/project';

const userGroups = createMappedRegion<ParamsGetUserGroup, UserGroup[]>([]);

export const useUserGroups = userGroups.useValue;

export const loadUserGroups = userGroups.loadBy(
    params => params,
    apiGetUserGroups
);

export const useUserGroupLoading = userGroups.useLoading;

const boundProjectsRegion = createMappedRegion<ParamsUserGroupId, Project[]>([]);

export const useBoundProjects = boundProjectsRegion.useValue;

export const loadBoundProjects = boundProjectsRegion.loadBy(
    params => params,
    apiGetBoundProjects
);

const useInfoRegion = createMappedRegion<ParamsBatchQueryUsers, UserDetail[]>([]);

export const useUserInfo = useInfoRegion.useValue;

export const loadUserInfo = useInfoRegion.loadBy(
    params => params,
    apiPostBatchQueryUsers
);
