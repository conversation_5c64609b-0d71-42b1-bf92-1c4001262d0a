import {createICodingInterface} from '@/utils/icode/api/createICodingInterface';

// iCoding Workspace状态
type WorkspaceStatus = '0' // online
    | '1' // offline
    | '2' // deleted
    | '3'; // restart

export type IDETypeNumber = 0 // for vscode
    | 1 // for idea community
    | 101 // for idea ultimate
    | 102 // for goland
    | 103 // for pycharm
    | 106 // for android studio
    | 107 // for clion
    | 108 // for phpstorm
    | 2; // for vscode on windows

export type IDETypeString = '0' // for vscode
    | '1' // for idea community
    | '101' // for idea ultimate
    | '102' // for goland
    | '103' // for pycharm
    | '106' // for android studio
    | '107' // for clion
    | '108' // for phpstorm
    | '2'; // for vscode on windows

export type AgentStatus = '0' | '1';

// 用户可用的开发机，包括自己的和从集团云资源申请下来的
export interface ResponseMachine {
    id: string;
    machineId?: string; // 分享的机器有
    hostname: string;
    ip: string;
    'server_port': string;
    // 方便类型推导
    status: WorkspaceStatus | string;
    ctoken: string;
    gtoken: string;
    ctime: string;
    mtime: string;
    type: 'personal';
    agentStatus: AgentStatus;
    'is_top': string | number;
    ideType: IDETypeString;
    machineStatus?: number;
}

export interface ExtraMachineFields {
    name?: string;
    shareUname?: string;
}

export type Machine = ResponseMachine & ExtraMachineFields;

interface ParamsGetMachineList {
    ideType: IDETypeNumber | 999; // ideType 999以获取全部开发机
}

// 获取开发机列表
export const apiGetMachineList = createICodingInterface<ParamsGetMachineList, {maclist: ResponseMachine[]}>(
    'GET', '/platform/machine/getmachinelist'
);

interface ParamsGetAddAgentCommand {
    ideType: IDETypeNumber;
    installType: 'machine' | 'docker';
}

interface ResultGetAddAgentCommand {
    installCmd: string;
    gtoken: string;
}

// 获取添加开发机的安装命令
export const apiGetAddAgentCommand = createICodingInterface<ParamsGetAddAgentCommand, ResultGetAddAgentCommand>(
    'GET', '/platform/agent/addagent'
);

// 检查开发机的安装状态
export const apiGetCheckMachine = createICodingInterface<{gtoken: string}, {host: string, ip: string}>(
    'GET', '/platform/machine/checkmachine'
);

// 删除开发机
export const apiPostDeleteMachine = createICodingInterface<{machineId: string}>(
    'POST',
    '/platform/machine/delmachine'
);

interface ExtensionCardConf {
    id: string;
    title: string;
    extensions: string[];
    preloadingId: string;
}

interface PreExtensionListRes {
    extensionCardConf: ExtensionCardConf[];
}

// 获取开发机可预装的插件列表
export const apiGetPreExtensionList = createICodingInterface<void, PreExtensionListRes>(
    'GET', '/platform/machine/getprelist'
);

// 执行插件预装
export const apiPostStartInstallExtension = createICodingInterface<{gtoken: string, list: string[]}>(
    'POST', '/platform/agent/installplugin'
);

// 检查预装插件的安装状态
export const apiPostCheckExtensionInstalled = createICodingInterface<{gtoken: string}>(
    'POST', '/platform/agent/checkplugin'
);

// 开发机下的工作空间
export interface ResponseWorkspace {
    cmd: string;
    hostname: string;
    id: string;
    isAgentOffline: boolean;
    isTop: number;
    machineId: string;
    name: string;
    path: string;
    status: string;
    mtime: string;
    isSharing: boolean;
    ideType: IDETypeString;
    panDomain?: string;
}

export interface Workspace extends ResponseWorkspace {
    shareUname?: string;
    shareRealName?: string;
    editionName?: string;
    editionPath?: string;
}

// 工作空间列表
export interface ResponseGetWorkspace {
    spacelist: ResponseWorkspace[];
}

interface ParamsGetWorkspaces {
    ideType: IDETypeNumber | 999;
}

// 获取工作空间列表
export const apiGetWorkspaces = createICodingInterface<ParamsGetWorkspaces, ResponseGetWorkspace>(
    'GET', '/platform/workspace/getworkspacelist'
);

export interface RestartWorkspaceParam {
    workspaceId: string;
}

// 重启工作空间
export const apiPostRestartWorkspace = createICodingInterface<RestartWorkspaceParam>(
    'POST', '/platform/workspace/restartworkspace'
);

interface ChangeWorkspaceNameParam {
    workspaceId: string;
    workspaceName: string;
}

interface ChangeWorkspaceNameRes {
    status: 0;
    message: string;
}

// 修改工作空间名称
export const apiPostChangeWorkspaceName = createICodingInterface<ChangeWorkspaceNameParam, ChangeWorkspaceNameRes>(
    'POST', '/platform/workspace/modifyworkspace'
);

interface ChangeMachineNameParam {
    machineId: string;
    machineName: string;
}

interface ChangeMachineNameResponse {
    code: number;
    message: string;
    result: string;
}

// 修改开发机名称
export const apiChangeMachineName = createICodingInterface<ChangeMachineNameParam, ChangeMachineNameResponse>(
    'POST', '/platform/machine/modifymachine'
);

interface ToggleTopWorkspaceParam {
    workspaceId: string;
    type: 0 | 1;
}

// 修改置顶状态
export const apiGetToggleTopWorkspace = createICodingInterface<ToggleTopWorkspaceParam, unknown>(
    'GET', '/platform/workspace/istopworkspace'
);

interface DelWorkspaceParam {
    workspaceId: string;
}

interface DelWorkspaceRes {
    status: 0;
    message: string;
}

export const apiPostDeleteWorkspace = createICodingInterface<DelWorkspaceParam, DelWorkspaceRes>(
    'POST', '/platform/workspace/delworkspace'
);

interface ParamsGetSharedWorkspaces {
    ideType: IDETypeNumber | 999;
}

export interface ResponseSharedWorkspace {
    machineId: string;
    workspaceId: string;
    name: string;
    path: string;
    hostname: string;
    status: string;
    shareUname: string;
    shareRealName: string;
    isTop: number;
    ctime: string;
    mtime: string;
    ideType: IDETypeString;
    panDomain?: string;
}

// 查询本人拥有的被分享工作区列表
export const apiGetSharedWorkspaces = createICodingInterface<ParamsGetSharedWorkspaces, ResponseSharedWorkspace[]>(
    'GET', '/platform/spaceshare/getsharedworkspaces'
);

export interface ParamsOfGetMachines {
    ideType?: IDETypeNumber | 999;
    page?: number;
    pageSize?: number;
}

export interface MachineNew {
    instanceId: string;
    imageId: ImageID;
    createTime: string;
    name: string;
    ip: string;
    vscode: Machine[];
    jetbrains: Machine[];
}

export interface MachinesWidthPageInfo {
    mclist: MachineNew[];
    totalCount: number;
    pageNum: number;
    pageSize: number;
}

export enum ImageID {
    Empty = '',
    MAMwYXGsc = 'm-AMwYXGsc',
    MEzR8PcuX = 'm-ezR8PcuX',
    MWY9Djam7 = 'm-WY9djam7',
}

export const apiGetMachines = createICodingInterface<ParamsOfGetMachines, MachinesWidthPageInfo>(
    'GET',
    '/platform/machine/getmachines'
);

export interface ShardWorkSpaceByMachine{
    hostname: string;
    ip: string;
    vscode: Machine[];
    jetbrains: Machine[];
}

// eslint-disable-next-line max-len
export const apiGetSharedWorkspacesByMachine = createICodingInterface<ParamsGetSharedWorkspaces, ShardWorkSpaceByMachine[]>(
    'GET', '/platform/spaceshare/getsharedworkspacesbymc'
);
