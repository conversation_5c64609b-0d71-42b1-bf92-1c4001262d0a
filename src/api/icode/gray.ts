import {createIcodeInterface} from '@/utils/createInterface/icode';

/**
 * 灰度流量检查接口参数
 */
interface ParamsPostGrayTraffic {
    username: string;
    feature: string;
}

/**
 * 灰度流量检查接口返回结果
 */
interface ResultPostGrayTraffic {
    inGrayTraffic: boolean;
}

/**
 * 检查用户是否在灰度流量中
 */
export const apiPostGrayTraffic = createIcodeInterface<
    ParamsPostGrayTraffic,
    ResultPostGrayTraffic
>(
    'POST',
    '/rest/git/api/gray/isInGrayTraffic'
);
