import type {MenuProps} from 'antd';

export const MIN_ZOOM = 0.1;
export const MAX_ZOOM = 10;
export const ZOOM_STEP = 0.1;
export const ZOOM_STEP_WHEEL = 0.00025;
export const MIN_ZOOM_WHEEL = 0.05;
export const MAX_ZOOM_WHEEL = 0.5;
export const WS_ZOOM_X = {
    min: 1,
    max: 1500,
    step: 10,
    default: 1,
};

export const WS_SPEED = {
    min: 0.5,
    max: 2,
    step: 0.01,
    default: 1,
};

export const WS_VOLUME = {
    min: 0,
    max: 1,
    step: 0.01,
    default: 1,
};

export const speedItems: MenuProps['items'] = [
    {
        label: '15.0x',
        key: '15',
    },
    {
        label: '10.0x',
        key: '10',
    },
    {
        label: '5.0x',
        key: '5',
    },
    {
        label: '3.0x',
        key: '3',
    },
    {
        label: '2.0x',
        key: '2',
    },
    {
        label: '1.5x',
        key: '1.5',
    },
    {
        label: '1.0x',
        key: '1',
    },
    {
        label: '0.5x',
        key: '0.5',
    },
];

