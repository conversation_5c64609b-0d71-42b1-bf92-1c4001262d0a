# Stage Context 使用指南

## 概述

Stage Context 是为 MessagePanel 添加的 React Context，用于支持用户自定义开发的 stage 组件。它提供了一个注册机制，允许开发者注册自定义的 stage 组件，并处理 `inPlatform` 类型的 action。

## 主要功能

1. **Stage 组件注册**: 允许动态注册和注销自定义 stage 组件
2. **inPlatform Action 处理**: 支持处理 `inPlatform` 类型的 action，并将所有参数传递给处理函数
3. **类型安全**: 提供完整的 TypeScript 类型定义

## 类型更新

### StageBase 接口
- `taskId` 现在是必需字段（之前是可选的）

### ChatAnchor 接口
- `taskId` 现在是必需字段
- 添加了 `tabId` 可选字段

### StepItem 接口
- 添加了 `taskId` 必需字段

### ChatAction 结构
现在支持以下新格式：
```typescript
{
    type: 'actions',
    actions: [
        {
            buttonType: 'primary',
            text: '全部采纳',
            right?: boolean;
            actionTypes: [
                'openWindow', 
                'jumpPlatform',
                'closeNotification',
                'inPlatform'  // 新增支持
            ],
            query: '全部采纳',
            anchor: {
                taskId: 'QWERTYGXCVBN', // 现在是必需的
                stageId: 'abc',
                stepId: 'def',
                tabId: 'aaaa',
            },
            enableConfetti?: boolean;
        }
    ],
}
```

## 使用方法

### 1. 创建自定义 Stage 组件

```typescript
import React from 'react';
import {StageComponentProps} from '../contexts/StageContext';

const MyCustomStage: React.FC<StageComponentProps> = ({
    taskId,
    stageId,
    data,
    onAction,
}) => {
    const handleCustomAction = () => {
        if (onAction) {
            onAction({
                text: '自定义操作',
                buttonType: 'primary',
                actionTypes: ['inPlatform'],
                anchor: {
                    taskId,
                    stageId,
                },
                query: '执行了自定义操作',
            });
        }
    };

    return (
        <div>
            <h3>自定义 Stage: {stageId}</h3>
            <button onClick={handleCustomAction}>执行操作</button>
        </div>
    );
};
```

### 2. 注册 Stage 组件

```typescript
import {useStageRegistry} from '../contexts/StageContext';

const MyComponent = () => {
    const {registerStage, unregisterStage} = useStageRegistry();

    useEffect(() => {
        registerStage('myCustomType', MyCustomStage);
        
        return () => {
            unregisterStage('myCustomType');
        };
    }, [registerStage, unregisterStage]);

    // ...
};
```

### 3. 使用 Stage 组件

```typescript
import {useStageComponent} from '../hooks/useStageComponent';

const StageRenderer = ({taskId, stageId, stageType, data}) => {
    const {StageComponent, stageProps} = useStageComponent({
        taskId,
        stageId,
        stageType,
        data,
    });

    if (!StageComponent) {
        return <div>未找到 stage 组件: {stageType}</div>;
    }

    return <StageComponent {...stageProps} />;
};
```

### 4. 处理 inPlatform Actions

在 MessagePanel 中传递 `onInPlatformAction` 处理函数：

```typescript
const handleInPlatformAction = (action: ChatAction, taskId?: string) => {
    console.log('处理 inPlatform action:', action, 'taskId:', taskId);
    // 在这里实现你的自定义逻辑
};

<MessagePanel 
    show={true} 
    onInPlatformAction={handleInPlatformAction} 
/>
```

## 文件结构

```
src/mcp/Playground/ChatArea/
├── contexts/
│   └── StageContext.tsx          # Stage Context 定义
├── hooks/
│   └── useStageComponent.ts      # Stage 组件使用 hook
├── components/
│   └── CustomStageExample.tsx    # 示例自定义 Stage 组件
└── MessagePanel.tsx              # 更新后的 MessagePanel
```

## 注意事项

1. 所有自定义 stage 组件都必须实现 `StageComponentProps` 接口
2. `taskId` 现在是必需字段，确保在所有相关接口中都提供了这个值
3. `inPlatform` action 会自动调用注册的处理函数，并传递完整的 action 对象和 taskId
4. 记得在组件卸载时注销已注册的 stage 组件，避免内存泄漏
