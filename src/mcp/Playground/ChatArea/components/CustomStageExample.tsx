import {FC, useEffect} from 'react';
import {Button, Card, Space} from 'antd';
import {StageComponentProps, useStageRegistry} from '../contexts/StageContext';

const CustomStageExample: FC<StageComponentProps> = ({
    taskId,
    stageId,
    data,
    onAction,
}) => {
    const handleCustomAction = () => {
        if (onAction) {
            onAction({
                text: '自定义操作',
                buttonType: 'primary',
                actionTypes: ['inPlatform'],
                anchor: {
                    taskId,
                    stageId,
                },
                query: '执行了自定义stage操作',
            });
        }
    };

    return (
        <Card title={`自定义Stage组件 - ${stageId}`} style={{margin: '16px 0'}}>
            <Space direction="vertical" style={{width: '100%'}}>
                <div>Task ID: {taskId}</div>
                <div>Stage ID: {stageId}</div>
                <div>Data: {JSON.stringify(data, null, 2)}</div>
                <Button type="primary" onClick={handleCustomAction}>
                    执行自定义操作
                </Button>
            </Space>
        </Card>
    );
};

export const useRegisterCustomStage = () => {
    const {registerStage, unregisterStage} = useStageRegistry();

    useEffect(
        () => {
            registerStage('customExample', CustomStageExample);

            return () => {
                unregisterStage('customExample');
            };
        },
        [registerStage, unregisterStage]
    );
};

export default CustomStageExample;
