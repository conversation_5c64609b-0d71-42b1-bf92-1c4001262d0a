import {useRef, useImperativeHandle, forwardRef, useCallback} from 'react';
import styled from '@emotion/styled';
import {Flex} from 'antd';
import {ChatAction} from '@/types/staff/element';
import {
    RESPONSIVE_SPACING,
} from './constants';
import MessageList from './MessageList';
import {StageProvider} from './contexts/StageContext';

interface MessagePanelProps {
    show: boolean;
    onInPlatformAction?: (action: ChatAction, taskId?: string) => void;
}

export interface MessagePanelRef {
    scrollToBottom: () => void;
}

const Container = styled.div<{ show: boolean }>`
    width: 100%;
`;

const MessagesContainer = styled.div`
    flex: 1;
    margin-top: ${RESPONSIVE_SPACING.MESSAGE_PANEL.MARGIN_TOP}px;
    overflow-y: auto;
    height: 100%;

    ::-webkit-scrollbar {
        display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;
`;

const MessagePanel = forwardRef<MessagePanelRef, MessagePanelProps>(
    ({show, onInPlatformAction}, ref) => {
        const messagesEndRef = useRef<HTMLDivElement>(null);

        const scrollToBottom = useCallback(
            () => {
                messagesEndRef.current?.scrollIntoView({behavior: 'smooth'});
            },
            []
        );

        useImperativeHandle(
            ref,
            () => ({
                scrollToBottom,
            }),
            [scrollToBottom]
        );

        return (
            <StageProvider onInPlatformAction={onInPlatformAction}>
                <Container show={show}>
                    <Flex vertical style={{height: '100%'}}>
                        <MessagesContainer>
                            <MessageList />
                            <div ref={messagesEndRef} />
                        </MessagesContainer>
                    </Flex>
                </Container>
            </StageProvider>
        );
    }
);

MessagePanel.displayName = 'MessagePanel';

export default MessagePanel;
