import {ChatAction} from '@/types/staff/element';
import {useStageContext} from '../contexts/StageContext';

export interface UseStageComponentProps {
    taskId: string;
    stageId: string;
    stageType: string;
    data?: any;
}

export const useStageComponent = ({
    taskId,
    stageId,
    stageType,
    data,
}: UseStageComponentProps) => {
    const {getStageComponent, handleInPlatformAction} = useStageContext();

    const StageComponent = getStageComponent(stageType);

    const handleAction = (action: ChatAction) => {
        if (action.actionTypes?.includes('inPlatform')) {
            handleInPlatformAction(action, taskId);
        }
    };

    return {
        StageComponent,
        stageProps: {
            taskId,
            stageId,
            data,
            onAction: handleAction,
        },
    };
};
