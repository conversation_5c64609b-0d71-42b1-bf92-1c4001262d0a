import {createMappedRegion} from 'region-react';
import {apiGetRawText} from '@/api/icode/binary';
import {DiffSourceResult} from './diffTypes';

interface TextKey {
    repo: string;
    commitId: string;
    filePath: string;
}

const rawTextRegion = createMappedRegion<TextKey, DiffSourceResult>(undefined);

export const resetAllRawText = rawTextRegion.resetAll;

export const loadRawText = rawTextRegion.loadBy(
    params => params,
    apiGetRawText,
    (state, result) => {
        const lines = result.value.split('\n');
        const lineCount = lines.length;
        return {
            text: result.value,
            length: result.length,
            lines,
            lineCount,
        };
    }
);

export const useRawText = rawTextRegion.useValue;
export const getRawText = rawTextRegion.getValue;
export const useRawTextLoading = rawTextRegion.useLoading;
