import {createRegion} from 'region-react';
import {MultipleChoiceCommentTdInfo} from '@/types/icode/comment';

const multipleCodesRegion = createRegion<MultipleChoiceCommentTdInfo[]>([]);

export const getMultipleCodes = multipleCodesRegion.getValue;

export const resetMultipleCodes = multipleCodesRegion.reset;

export const setMultipleCodes = multipleCodesRegion.set;

const firstClickTdLindInfoRegion = createRegion<MultipleChoiceCommentTdInfo>();

export const getFirstClickTdLineInfo = firstClickTdLindInfoRegion.getValue;

export const resetFirstClickTdLineInfo = firstClickTdLindInfoRegion.reset;

export const setFirstClickTdLineInfo = firstClickTdLindInfoRegion.set;

const rangeCommentMouseDownTimeRegion = createRegion<number>();

export const getRangeCommentMouseDownTime = rangeCommentMouseDownTimeRegion.getValue;

export const resetRangeCommentMouseDownTime = rangeCommentMouseDownTimeRegion.reset;

export const setRangeCommentMouseDownTime = rangeCommentMouseDownTimeRegion.set;
