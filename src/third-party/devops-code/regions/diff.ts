import {createMappedRegion} from 'region-react';
import {cloneDeep} from 'lodash';
import {parseDiff} from 'react-diff-view';
import {DiffType} from '@baidu/devops-code/es/DiffView/types';
import {apiGetRawDiff} from '@/api/icode/binary';
import {calculateHunkMetrics} from '@/utils/icode/diffMetrics';
import {DiffTypeWithInitialHunks} from './diffTypes';

interface ParamsFetchDiff {
    repo: string;
    oldCommitId: string;
    newCommitId: string;
    fileName: string;
    enableMaxPayload?: boolean;
}

export const getDiffFilePath = (diff: DiffType, path: string) => {
    // 曾经的逻辑
    // const filePath = ['rename', 'copy'].includes(data.type) ? data.oldPath : path;
    // 如果这里有问题，可能需要传入 path，因为中文解析似乎不是很对
    // 因为两端都用了同一个函数，即使中文解析的不好，依旧可以适配到相同的值
    if (diff.oldPath === '/dev/null') {
        // 防止中文问题
        // return diff.newPath;
        return path ?? diff.newPath;
    }
    if (diff.oldPath !== diff.newPath) {
        return diff.oldPath;
    }
    return path ?? diff.newPath;
};

interface DiffKey {
    repo: string;
    oldCommitId: string;
    newCommitId: string;
    fileName: string;
}

export const EMPTY_DIFF: DiffTypeWithInitialHunks = {} as DiffTypeWithInitialHunks;

const rawDiffRegion = createMappedRegion<DiffKey, DiffTypeWithInitialHunks>(EMPTY_DIFF, {strategy: 'acceptFirst'});

export const getRawDiff = rawDiffRegion.getValue;
export const setRawDiff = rawDiffRegion.set;
export const useRawDiff = rawDiffRegion.useValue;
export const useRawDiffError = rawDiffRegion.useError;
export const resetAllRawDiff = rawDiffRegion.resetAll;
export const useRawDiffLoading = rawDiffRegion.useLoading;

const fetchDiff = async ({repo, fileName, oldCommitId, newCommitId, enableMaxPayload}: ParamsFetchDiff) => {
    const ref = await apiGetRawDiff({
        repo,
        aCommitId: oldCommitId,
        bCommitId: newCommitId,
        fileName,
        enableMaxPayload,
    });
    const diffString = ref.value;

    if (typeof diffString !== 'string') {
        // @ts-expect-error
        throw new Error(diffString?.message);
    }
    if (!diffString.startsWith('diff')) {
        const tryParseData = () => {
            const data = diffString ? JSON.parse(diffString) : {message: ''};
            throw new Error(data?.message);
        };
        try {
            tryParseData();
        }
        catch (e) {
            throw e;
        }
    }
    const [result] = parseDiff(diffString, {nearbySequences: 'zip'});
    return result;
};

export const loadRawDiff = rawDiffRegion.loadBy(
    ({repo, oldCommitId, newCommitId, fileName}) => ({repo, oldCommitId, newCommitId, fileName}),
    fetchDiff,
    (_, result) => {
        const {hunksMaxContentLength, hunksModifyChangeCount} = calculateHunkMetrics(result?.hunks);
        return {
            ...result,
            hunksExpanded: false,
            hunksMaxContentLength,
            hunksModifyChangeCount,
            initialHunks: cloneDeep(result?.hunks),
        };
    }
);

export const initRawDiffHunks = (diffKey: DiffKey) => {
    setRawDiff(diffKey, diff => {
        const {hunksMaxContentLength, hunksModifyChangeCount} = calculateHunkMetrics(diff?.initialHunks);
        return {
            ...diff,
            hunks: cloneDeep(diff.initialHunks),
            hunksExpanded: false,
            hunksModifyChangeCount,
            hunksMaxContentLength,
        };
    });
};
