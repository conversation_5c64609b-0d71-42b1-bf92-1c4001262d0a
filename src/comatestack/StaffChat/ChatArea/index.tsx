import styled from '@emotion/styled';
import {useEffect} from 'react';
import {useShowHistory} from '@/regions/staff/showHistory';
import {getDongTingSDKV3} from '@/utils/setup-dongting-sdk';
import Header from './Header';
import {MessagePanel} from './MessagePanel';
import {HistoryPanel} from './HistoryPanel';

const Container = styled.div`
    height: 100vh;
    display: flex;
    flex-direction: column;
    min-width: 400px;
    align-items: center;
`;

const Content = styled.div`
    flex: 1;
    width: 100%;
    max-width: 800px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
    overflow: hidden;
    padding: 16px 24px;
    z-index: 1;
`;

const ChatArea = () => {
    const showHistory = useShowHistory();

    useEffect(
        () => {
            const timer = setTimeout(() => {
                getDongTingSDKV3().then(sdk => {
                    sdk.setupSidebar();
                });
            }, 60000);

            return () => {
                clearTimeout(timer);
                getDongTingSDKV3().then(sdk => sdk.destroySidebar());
            };
        },
        []
    );

    return (
        <Container>
            <Header />
            <Content>
                {showHistory ? <HistoryPanel /> : <MessagePanel />}
            </Content>
        </Container>
    );
};

export default ChatArea;
