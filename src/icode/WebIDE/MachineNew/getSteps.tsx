import {ReactElement, ReactNode} from 'react';
import {<PERSON><PERSON>} from '@panda-design/components';
import {IdeType, AgentHost} from '@/types/icode/webIDE';
import {invokeAiIDEClientConnect} from '../InvokeClientOperation/invokeClients';
import {DownloadAiIDEModal} from '../DownloadAiIDEModal';
import {openDownloadModal} from '../DownloadAiIDEModal/region';
import LoginStep from './LoginStep';
import FetchCommandStep from './FetchCommandStep';
import CheckMachineStep from './CheckMachineStep';
import SelectExtensionStep from './SelectExtensionStep';
import SelectIdeStep from './SelectIdeStep';
import {StepMargin, Divider} from './styledComponents';
import AiIdeConnectStep from './AiIdeConnectStep';

const openAiIDE = () => {
    invokeAiIDEClientConnect().catch(() => {
        openDownloadModal();
    });
};

interface MachineAddStepRenderProps {
    ideType: IdeType;
    hostType: AgentHost;
    gtoken?: string;
    command?: string;
    isFetching: boolean;
    isMachineDetected: boolean;
    showBackToConsole: () => void;
    refresh: () => void;
    onHostChange: (hostType: AgentHost) => void;
    onIdeChange: (ideType: IdeType) => void;
    onMachineDetected: (value: string) => void;
}

interface MachineAddStepProps {
    title: ReactNode;
    key: string;
    render: (props: MachineAddStepRenderProps) => ReactElement;
}

interface GetStepsParams {
    canPreInstallExtensions: boolean;
    ideType: IdeType;
    onlyVscode?: boolean;
    onlyJetbrains?: boolean;
}

export const getSteps = ({
    canPreInstallExtensions,
    onlyVscode,
    onlyJetbrains,
    ideType,
}: GetStepsParams): MachineAddStepProps[] => {
    const steps: MachineAddStepProps[] = [
        {
            title: '选择IDE类型',
            key: '选择IDE类型',
            render: ({ideType, isMachineDetected, onIdeChange}) => (
                <StepMargin>
                    <SelectIdeStep
                        onlyJetbrains={onlyJetbrains}
                        onlyVscode={onlyVscode}
                        ideType={ideType}
                        onIdeChange={onIdeChange}
                        deactivated={isMachineDetected}
                    />
                    <Divider />
                </StepMargin>
            ),
        },
    ];
    if (ideType === 'aiIDE') {
        steps.push(
            {
                title: (
                    <>在Comate AI IDE中进行远程连接，<Button type="link" onClick={openAiIDE}>点击跳转</Button></>
                ),
                key: '在Comate AI IDE中进行远程连接，点击跳转',
                render: () => (
                    <StepMargin>
                        <AiIdeConnectStep />
                        <DownloadAiIDEModal />
                    </StepMargin>
                ),
            }
        );
    }
    else {
        steps.push(
            {
                title: '登录目标机器',
                key: '登录目标机器',
                render: () => (
                    <StepMargin>
                        <LoginStep />
                        <Divider />
                    </StepMargin>
                ),
            },
            {
                title: '安装Agent',
                key: '安装Agent',
                render: ({command, hostType, onHostChange, isMachineDetected, isFetching, ideType}) => (
                    <StepMargin>
                        <FetchCommandStep
                            command={command}
                            host={hostType}
                            onHostChange={onHostChange}
                            deactivated={isMachineDetected}
                            isFetching={isFetching}
                            ideType={ideType}
                        />
                        <Divider />
                    </StepMargin>
                ),
            },
            {
                title: '检测开发机是否安装Agent成功',
                key: '检测开发机是否安装Agent成功',
                render: ({gtoken, refresh, onMachineDetected}) => (
                    <StepMargin>
                        <CheckMachineStep
                            gtoken={gtoken}
                            refresh={refresh}
                            onMachineDetected={onMachineDetected}
                        />
                        {canPreInstallExtensions && <Divider />}
                    </StepMargin>
                ),
            }
        );
    }
    if (canPreInstallExtensions) {
        steps.push({
            title: '预装插件，快速准备环境（可跳过）',
            key: '预装插件，快速准备环境（可跳过）',
            render: ({gtoken, isMachineDetected, showBackToConsole}) => (
                <SelectExtensionStep
                    gtoken={gtoken}
                    isVisible={isMachineDetected}
                    onFinish={showBackToConsole}
                />
            ),
        });
    }
    return steps;
};
