import {Steps} from 'antd';
import styled from '@emotion/styled';

export const OptionCard = styled.div`
    width: 280px;
    height: 84px;
    padding-left: 20px;
    background-size: 100%;
    display: flex;
    gap: 16px;
    align-items: center;
    position: relative;
    cursor: pointer;
    border: 1px solid transparent;

    &:hover {
        border: 1px solid var(--color-brand-6);
    }
`;

interface CardBoxProps {
    deactivated?: boolean;
}

export const CardBox = styled.div<CardBoxProps>`
    display: flex;
    margin: 20px 0;
    gap: 16px;
    flex-wrap: wrap;

    ${({deactivated}) => deactivated && `
        opacity: .5;
    `}
`;

export const StepIcon = styled.div`
    width: 20px;
    height: 20px;
    margin-top: 2px;
    border-radius: 50%;
    border: 1px solid var(--color-gray-8);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: var(--color-gray-8);
`;

export const StepMargin = styled.div`
    margin-top: 12px;
`;

export const Divider = styled.div`
    width: 100%;
    height: 1px;
    border-top: 1px dashed var(--color-gray-5);
    margin: 20px 0 5px;
`;

export const StepsContainer = styled(Steps)`
    margin: 20px 0 !important;
    min-width: 1250px;
`;
