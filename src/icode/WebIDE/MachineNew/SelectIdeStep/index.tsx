import {useCallback} from 'react';
import {partial} from 'lodash';
import {IdeType} from '@/types/icode/webIDE';
import {CardBox} from '../styledComponents';
import {
    vscodeCardCss,
    ideaCommunityCardCss,
    clionCardCss,
    ideaUltimateCardCss,
    golandCardCss,
    androidStudioCardCss,
    phpstormCardCss,
    pycharmCardCss,
    aiIdeCardCss,
} from './bgs';
import c from './index.module.less';
import IdeCard from './IdeCard';

interface Props {
    ideType: IdeType;
    onIdeChange: (type: IdeType) => void;
    deactivated: boolean;
    onlyVscode?: boolean;
    onlyJetbrains?: boolean;
}

const SelectIdeStep = ({ideType, onIdeChange, onlyVscode, onlyJetbrains, deactivated}: Props) => {
    const handleIdeTypeChange = useCallback(
        (ideType: IdeType) => {
            if (deactivated) {
                return;
            }
            onIdeChange(ideType);
        },
        [deactivated, onIdeChange]
    );

    return (
        <>
            <CardBox deactivated={deactivated}>
                {!onlyJetbrains && !onlyVscode && (
                    <IdeCard
                        onClick={partial(handleIdeTypeChange, 'aiIDE')}
                        className={aiIdeCardCss}
                        checked={ideType === 'aiIDE'}
                        type="aiIDE"
                    >
                        <div className={c.nameWrapper}>
                            <div className={c.name}>Comate AI IDE</div>
                        </div>
                    </IdeCard>
                )}

                {!onlyVscode && (
                    <>
                        <IdeCard
                            onClick={partial(handleIdeTypeChange, 'ideaCommunity')}
                            className={ideaCommunityCardCss}
                            checked={ideType === 'ideaCommunity'}
                            type="ideaCommunity"
                        >
                            <div className={c.nameWrapper}>
                                <div className={c.name}>IntelliJ IDEA</div>
                                <div className={c.secondaryName}>社区版</div>
                            </div>
                        </IdeCard>

                        <IdeCard
                            onClick={partial(handleIdeTypeChange, 'ideaUltimate')}
                            className={ideaUltimateCardCss}
                            checked={ideType === 'ideaUltimate'}
                            type="ideaUltimate"
                        >
                            <div className={c.nameWrapper}>
                                <div className={c.name}>IntelliJ IDEA</div>
                                <div className={c.secondaryName}>专业版</div>
                            </div>
                        </IdeCard>

                        <IdeCard
                            onClick={partial(handleIdeTypeChange, 'goland')}
                            className={golandCardCss}
                            checked={ideType === 'goland'}
                            type="goland"
                        >
                            <div className={c.nameWrapper}>
                                <div className={c.name}>GoLand</div>
                            </div>
                        </IdeCard>
                        <IdeCard
                            onClick={partial(handleIdeTypeChange, 'pycharm')}
                            className={pycharmCardCss}
                            checked={ideType === 'pycharm'}
                            type="pycharm"
                        >
                            <div className={c.nameWrapper}>
                                <div className={c.name}>PyCharm</div>
                                <div className={c.secondaryName}>社区版</div>
                            </div>
                        </IdeCard>

                        <IdeCard
                            onClick={partial(handleIdeTypeChange, 'clion')}
                            className={clionCardCss}
                            checked={ideType === 'clion'}
                            type="clion"
                        >
                            <div className={c.nameWrapper}>
                                <div className={c.name}>CLion</div>
                            </div>
                        </IdeCard>

                        <IdeCard
                            onClick={partial(handleIdeTypeChange, 'phpstorm')}
                            className={phpstormCardCss}
                            checked={ideType === 'phpstorm'}
                            type="phpstorm"
                        >
                            <div className={c.nameWrapper}>
                                <div className={c.name}>PhpStorm</div>
                            </div>
                        </IdeCard>

                        <IdeCard
                            onClick={partial(handleIdeTypeChange, 'androidStudio')}
                            className={androidStudioCardCss}
                            checked={ideType === 'androidStudio'}
                            type="androidStudio"
                        >
                            <div className={c.nameWrapper}>
                                <div className={c.name}>Android Studio</div>
                            </div>
                        </IdeCard>
                    </>
                )}
                {!onlyJetbrains && (
                    <IdeCard
                        onClick={partial(handleIdeTypeChange, 'vscode')}
                        className={vscodeCardCss}
                        checked={ideType === 'vscode'}
                        type="vscode"
                    >
                        <div className={c.nameWrapper}>
                            <div className={c.name}>VS Code</div>
                        </div>
                    </IdeCard>
                )}
            </CardBox>
        </>
    );
};

export default SelectIdeStep;
