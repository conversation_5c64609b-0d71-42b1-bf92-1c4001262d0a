import {ReactNode} from 'react';
import {css, cx} from '@emotion/css';
import {IDEProductLabel} from '@/components/icode/IDEProductLabel';
import {IdeType} from '@/types/icode/webIDE';
import checkSrc from '@/assets/icode/webide/blueCheck.png';
import {OptionCard} from '../styledComponents';


const checkedWrapper = css`
    border: 1px solid #317FF5 !important;
`;

export const checkedCss = css`
    height: 16px;
    width: 16px;
    background-image: url(${checkSrc});
    background-size: 100%;
    position: absolute;
    top: 8px;
    right: 8px;
`;


interface Props {
    className?: string;
    onClick?: () => void;
    children?: ReactNode;
    checked: boolean;
    type: IdeType | 'linux' | 'docker';
}

const IdeCard = ({className, children, type, onClick, checked}: Props) => {
    return (
        <OptionCard className={cx(className, checked ? checkedWrapper : '')} onClick={onClick}>
            <IDEProductLabel ideType={type as IdeType} size="large" showLabel={false} />
            {children}
            {checked && <div className={checkedCss} />}
        </OptionCard>
    );
};

export default IdeCard;

