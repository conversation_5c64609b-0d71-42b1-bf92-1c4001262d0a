import {css} from '@emotion/css';
import vscodeCardSrc from '@/assets/icode/webide/bg-vscode.png';
import ideaCommunityCardSrc from '@/assets/icode/webide/bg-intellij-idea-community.png';
import ideaUltimateCardSrc from '@/assets/icode/webide/bg-intellij-idea-ultimate.png';
import golandCardSrc from '@/assets/icode/webide/bg-goland.png';
import pycharmCardSrc from '@/assets/icode/webide/bg-pycharm.png';
import clionCardSrc from '@/assets/icode/webide/bg-clion.png';
import phpstormCardSrc from '@/assets/icode/webide/bg-phpstorm.png';
import androidStudioCardSrc from '@/assets/icode/webide/bg-android-studio.png';
import aiIdeCardSrc from '@/assets/icode/webide/bg-aiide.png';

export const vscodeCardCss = css`
    background-image: url(${vscodeCardSrc});
`;

export const ideaCommunityCardCss = css`
    background-image: url(${ideaCommunityCardSrc});
`;

export const ideaUltimateCardCss = css`
    background-image: url(${ideaUltimateCardSrc});
`;

export const golandCardCss = css`
    background-image: url(${golandCardSrc});
`;

export const pycharmCardCss = css`
    background-image: url(${pycharmCardSrc});
`;

export const clionCardCss = css`
    background-image: url(${clionCardSrc});
`;

export const phpstormCardCss = css`
    background-image: url(${phpstormCardSrc});
`;

export const androidStudioCardCss = css`
    background-image: url(${androidStudioCardSrc});
`;

export const aiIdeCardCss = css`
    background-image: url(${aiIdeCardSrc});
`;
