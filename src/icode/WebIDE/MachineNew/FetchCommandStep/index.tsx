/**
 * @file 安装Agent步骤
 */
import {useState, useCallback} from 'react';
import {Popover, Typography, Input} from 'antd';
import {AgentHost, IdeType} from '@/types/icode/webIDE';
import {AnyEvent} from '@/types/icode/common';
import {CardBox} from '../styledComponents';
import {isIntellijIdeType} from '../utils';
import IdeCard from '../SelectIdeStep/IdeCard';
import Command from './Command';
import {dockerCardCss, linuxCardCss} from './bgs';
import c from './index.module.less';

interface Props {
    command?: string;
    host: AgentHost;
    onHostChange: (host: AgentHost) => void;
    deactivated: boolean;
    isFetching: boolean;
    ideType: IdeType;
}

const FetchCommandStep = ({command, host, onHostChange, deactivated, isFetching, ideType}: Props) => {
    const [path, setPath] = useState<string>('');

    const handlePathChange = useCallback(
        (e: AnyEvent) => {
            setPath(e.target.value);
        },
        []
    );

    const handleHostChange = useCallback(
        (host: AgentHost) => {
            if (deactivated) {
                return;
            }
            onHostChange(host);
        },
        [deactivated, onHostChange]
    );

    return (
        <>
            <CardBox deactivated={deactivated}>
                <IdeCard
                    checked={host === 'linux'}
                    type="linux"
                    onClick={() => handleHostChange('linux')}
                    className={linuxCardCss}
                >
                    <div className={c.agentLabel}>Linux</div>
                </IdeCard>

                {(ideType === 'vscode' || ideType === 'ideaCommunity') && (
                    <IdeCard
                        checked={host === 'docker'}
                        type="docker"
                        onClick={() => handleHostChange('docker')}
                        className={dockerCardCss}
                    >
                        <div className={c.agentLabel}>Docker</div>
                    </IdeCard>
                )}
            </CardBox>
            <div className={c.procedures}>
                {host === 'docker' && (
                    <div className={c.inputContainer}>
                        <span className={c.inputLabel}>挂载目录</span>
                        <Popover content={<div>需要在容器内访问宿主机文件时必填</div>}>
                            <div className={c.hintIcon}>?</div>
                        </Popover>
                        <Input
                            className={c.pathInput}
                            onChange={handlePathChange}
                            value={path}
                            disabled={deactivated}
                            placeholder="挂载宿主机目录，通常为代码所在目录或家目录（绝对路径）"
                        />
                    </div>
                )}
                <Command command={command} isFetching={isFetching} host={host} path={path} />
                {(isIntellijIdeType(ideType)) && (
                    <div className={c.reminder}>
                        特别提醒：<Typography.Text type="danger">为保证您的使用体验，请确保开发机的可用内存在4G及以上</Typography.Text>
                    </div>
                )}
            </div>
        </>
    );
};
export default FetchCommandStep;
