// eslint-disable-next-line complexity
export const isLinuxOnlyIdeType = (type: string) => {
    switch (type) {
        case 'ideaUltimate':
        case 'aiIDE':
        case 'goland':
        case 'pycharm':
        case 'clion':
        case 'phpstorm':
        case '101':
        case '102':
        case '103':
        case '106':
        case '107':
        case '108':
            return true;
        default:
            return false;
    }
};

// eslint-disable-next-line complexity
export const isIntellijIdeType = (type: string) => {
    switch (type) {
        case 'ideaCommunity':
        case 'ideaUltimate':
        case 'goland':
        case 'pycharm':
        case 'clion':
        case 'phpstorm':
        case 'androidStudio':
        case '1':
        case '101':
        case '102':
        case '103':
        case '106':
        case '107':
        case '108':
            return true;
        default:
            return false;
    }
};
