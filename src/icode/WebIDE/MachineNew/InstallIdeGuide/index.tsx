import {useCallback, useEffect, useState} from 'react';
import {Space, Steps} from 'antd';
import {useBoolean, useRequestCallback} from 'huse';
import {isEmpty} from 'lodash';
import styled from '@emotion/styled';
import {IconInfoCircleSolid} from '@baidu/ee-icon';
import {marginTop} from '@panda-design/components';
import {apiGetAddAgentCommand} from '@/api/icode/webIDE';
import {AgentHost, IdeType} from '@/types/icode/webIDE';
import {Link} from '@/utils/icode/link';
import {IconVscode} from '@/icons-icode/product';
import Notice from '@/design/icode/Notice';
import {translateIdeTypeForApi} from '../../utils';
import {StepIcon, StepsContainer} from '../styledComponents';
import {getSteps} from '../getSteps';
import {isLinuxOnlyIdeType} from '../utils';
import jetbrains from '../../assets/jetbrains.png';


const Text = styled.span`
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
`;

const Title = styled.span`
    font-size: 20px;
    font-weightL 500;
`;

interface Props {
    type: 'newMachine' | 'vscode' | 'jetbrains';
}

const InstallIdeGuide = ({type}: Props) => {
    const [ideType, setIdeType] = useState<IdeType>(
        type === 'vscode'
            ? 'vscode'
            : type === 'jetbrains'
                ? 'ideaCommunity'
                : 'aiIDE'
    );
    const [hostType, setHostType] = useState<AgentHost>('linux');
    const [detectedMachine, setDetectedMachine] = useState<string | null>(null); // ip of the detected machine
    const [canGoBackToConsole, {on: showBackToConsole}] = useBoolean(false);
    const [canPreInstallExtensions, setCanPreInstallExtensions] = useState(true);
    const [refresh, result] = useRequestCallback(
        apiGetAddAgentCommand,
        {
            ideType: translateIdeTypeForApi(ideType, hostType),
            installType: hostType === 'docker' ? 'docker' : 'machine',
        }
    );
    const isFetching = result.pending;
    const {gtoken, installCmd: command} = result.data || {};
    const handleIdeTypeChange = useCallback(
        (ideType: IdeType) => {
            // NOTE: 目前 VSCode 有 Linux、Docker 和 Windows 三种版本，IDEA社区版 有 Linux、Docker 两种版本，其余均只有 Linux 一种版本
            // 因此需要靠以下判断来调整hostType
            // 例：先选中 Windows，再选中 IDEA社区版，则自动调整 hostType 至 linux
            if (ideType === 'ideaCommunity' && hostType === 'windows') {
                setHostType('linux');
            }
            if (isLinuxOnlyIdeType(ideType) && hostType !== 'linux') {
                setHostType('linux');
            }
            setIdeType(ideType);
        },
        [hostType]
    );

    useEffect(
        () => {
            refresh();
        },
        [ideType, hostType, refresh]
    );

    useEffect(
        () => {
            const canPreInstallExtensions = ideType === 'vscode';
            setCanPreInstallExtensions(canPreInstallExtensions);
            const isMachineDetected = !isEmpty(detectedMachine);
            isMachineDetected && showBackToConsole();
        },
        [detectedMachine, ideType, showBackToConsole]
    );
    return (
        <>
            {type !== 'newMachine' && (
                <>
                    <Space size={16}>
                        <Title>
                            {
                                type === 'vscode'
                                    ? <IconVscode style={{width: 20, marginRight: 4}} />
                                    : <img src={jetbrains} style={{width: 20, marginRight: 4}} />
                            }

                            {type === 'vscode' ? 'VS Code' : 'Jetbrains 系列'}
                        </Title>
                        <Text>iCoding-VS Code 和 Jetbrains 系列IDE进入维护状态，推荐您使用 AI IDE</Text>
                    </Space>
                    <Notice type="info" icon={<IconInfoCircleSolid />} className={marginTop(20)}>
                        当前开发机暂未安装 {type === 'vscode' ? 'VS Code' : 'Jetbrains 系列 IDE'}，可按照如下方式安装
                    </Notice>
                </>
            )}
            <StepsContainer direction="vertical" size="small">
                {getSteps({
                    canPreInstallExtensions,
                    ideType,
                    onlyJetbrains: type === 'jetbrains',
                    onlyVscode: type === 'vscode',
                }).map((step, index) => (
                    <Steps.Step
                        status="process"
                        key={step.key}
                        title={step.title}
                        icon={<StepIcon>{index + 1}</StepIcon>}
                        description={step.render({
                            ideType,
                            hostType,
                            gtoken,
                            command,
                            isFetching,
                            isMachineDetected: !isEmpty(detectedMachine),
                            showBackToConsole,
                            refresh,
                            onHostChange: setHostType,
                            onIdeChange: handleIdeTypeChange,
                            onMachineDetected: setDetectedMachine,
                        })}
                    />
                ))}
            </StepsContainer>
            <div style={{height: '100px'}}>
                {canGoBackToConsole && <span>添加成功！<Link to={'/dev/resource'}>点击返回控制台查看</Link></span>}
            </div>
        </>
    );
};

export default InstallIdeGuide;

