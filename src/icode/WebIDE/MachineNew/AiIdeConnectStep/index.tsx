import styled from '@emotion/styled';
import {Flex} from 'antd';
import SvgArrows from '@/assets/icode/webide/Arrows';

const ContentWrapper = styled(Flex)`
    background: linear-gradient(
        81.84deg, rgba(49, 183, 245, 0.06) 16.09%, rgba(168, 227, 255, 0.12) 47.27%, rgba(49, 183, 245, 0.09) 78.45%
    );
    font-size: 12px;
    line-height: 20px;
    height: 90px;
    width: 280px;
    padding: 16px;
    z-index: 2;
    position: relative;
    margin-top: 8px;
`;

const StyledNum = styled.span`
    font-family: D-DIN-PRO;
    font-weight: 500;
    font-size: 40px;
    line-height: 100%;
    letter-spacing: 0px;
    position: absolute;
    right: 2px;
    top: 2px;
    color: rgba(49, 186, 245, 0.06);
`;


interface StepProps {
    title: string;
    content: string;
    index: number;
}

const StepContent = ({title, content, index}: StepProps) => {
    return (
        <div>
            <Flex align="center" style={{fontWeight: 600}}>
                STEP{index + 1}
                <SvgArrows style={{height: 10, marginLeft: 4}} />
            </Flex>
            <ContentWrapper vertical>
                <span style={{color: '#333', fontWeight: 500}}>{title}</span>
                <span style={{color: '#666'}}>{content}</span>
                <StyledNum>0{index + 1}</StyledNum>
            </ContentWrapper>
        </div>
    );
};

const steps = [
    {
        title: '开发机',
        content: '系统版本支持 Ubuntu20.04+ 或 CentOS7.5，跳转Comate AI IDE进行远程连接',
    },
    {
        title: 'Comate AI IDE',
        content: '使用内网账号登录，打开 Remote 服务插件，添加开发机地址密码进行连接',
    },
    {
        title: '连接成功',
        content: '连接成功后，后续可展示已远程连接的开发机工作区和代码空间代码库',
    },
];

const AiIdeConnectStep = () => {
    return (
        <Flex gap={16}>
            {steps.map(
                ({title, content}, index) => (
                    <StepContent title={title} content={content} index={index} key={title} />
                )
            )}
        </Flex>
    );
};

export default AiIdeConnectStep;

