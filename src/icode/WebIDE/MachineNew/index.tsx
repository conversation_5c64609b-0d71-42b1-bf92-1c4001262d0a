import {marginTop} from '@panda-design/components';
import {IconInfoCircleSolid} from '@baidu/ee-icon';
import Notice from '@/design/icode/Notice';
import {MachineApplyLink} from '@/links/icode/webIDE';
import PageHeader from '@/design/icode/PageHeader';
import InstallIdeGuide from './InstallIdeGuide';

const MachineNew = () => {

    return (
        <>
            <PageHeader title="添加开发机" />
            <Notice type="info" icon={<IconInfoCircleSolid />} className={marginTop(20)}>
                iCoding-VS Code和Jetbrains系列IDE进入维护状态，推荐您使用AI IDE。若无开发机，可点击
                <MachineApplyLink>申请开发机</MachineApplyLink>
            </Notice>
            <InstallIdeGuide type="newMachine" />
        </>
    );
};

export default MachineNew;
