import {MouseEvent} from 'react';
import styled from '@emotion/styled';
import {Button, IconClose} from '@panda-design/components';
import {useBoolean} from 'huse';
import banner from '@/assets/icode/webide/banner.jpg';

const Wrapper = styled.div`
    cursor: pointer;
    position: relative;
    margin-top: 16px;
`;

const StyledButton = styled(Button)`
    position: absolute;
    right: 16px;
    top: 16px;
    z-index: 10;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    color: #fff !important;
    background-color: #ffffff20 !important;
    &:hover {
        color: #fff !important;
        background-color: #ffffff40 !important;
    }
`;

const handleClick = () => {
    window.open('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/z97pwhZizu/hy296wAZ7z/5MEnx3Ojq6lyZ5');
};


const Header = () => {
    const [showBanner, {off: hide}] = useBoolean(true);
    const handleClose = (e: MouseEvent) => {
        e.stopPropagation();
        hide();
    };
    return (
        <Wrapper onClick={handleClick}>
            {showBanner && (
                <img src={banner} style={{width: '100%'}} />
            )}
            <StyledButton onClick={handleClose} type="text" icon={<IconClose />} />
        </Wrapper>
    );
};

export default Header;
