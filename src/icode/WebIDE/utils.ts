import {isEmpty} from 'lodash';
import {from, Notification} from 'rxjs';
import {ICODING_ORIGIN} from '@/constants/icode/urls';
import {Workspace, Machine, ResourceStatusInfo, WebIDEResource} from '@/api/icode/webIDE';
import {AgentHost, ICodingDesktopClientType, IdeType, ResourceShareType, ResourceType} from '@/types/icode/webIDE';
import {FailureKind} from '@/hooks/icode/webIDE/usePaymentAccountSelectModal';
import {REAL_TEMPLATE_ID_MAP} from '@/constants/icode/webIDE';
import {
    handleStatusType as handleMachineStatusType,
    handleStatusText as handleMachineStatusText,
} from '../IDEMachine/MachineList/utils';
import {
    handleStatusType as handleCodeSpaceStatusType,
    handleStatusText as handleCodeSpaceStatusText,
} from './ResourceList/utils';

/**
 * 如果status.swanEditorUrl字段为空，就拼一个url，否则直接用swanEditorUrl
 * @param status ResourceStatusInfo
 * @param query optional
 */
export const getResourceICodingEditorLink = (status: ResourceStatusInfo, query?: URLSearchParams) => {
    return isEmpty(status.swanEditorUrl)
        ? `${ICODING_ORIGIN}/workbench/${status.mid}${query ? `/?${query}` : ''}`
        : status.swanEditorUrl;
};

interface Status {
    type: 'info' | 'success' | 'error' | 'warning' | 'pending';
    label: string;
}

export const resolveResourceStatus = (status: string | undefined, resourceType: ResourceType): Status => {
    switch (resourceType) {
        case 'devServer':
            return {
                type: handleMachineStatusType(status),
                label: handleMachineStatusText(status),
            };
        case 'codeSpace':
            return {
                type: handleCodeSpaceStatusType(status),
                label: handleCodeSpaceStatusText(status),
            };
        default: {
            return {
                type: 'info',
                label: '未知',
            };
        }
    }
};

export const getResourceTypeDisplayName = (resourceType: ResourceType) => {
    switch (resourceType) {
        case 'devServer':
            return '开发机';
        case 'codeSpace':
            return '代码空间';
        default:
            return '未知';
    }
};

export const getResourceShareTypeDisplayName = (resourceType: ResourceShareType | undefined) => {
    switch (resourceType) {
        case 'workspace':
            return '工作区';
        case 'codespace':
            return '代码空间';
        default:
            return '未知';
    }
};

export const translateIdeTypeForApi = (ideType: IdeType, agentHost: AgentHost) => {
    switch (ideType) {
        case 'vscode':
            return agentHost === 'windows' ? 2 : 0;
        case 'ideaCommunity':
            return 1;
        case 'ideaUltimate':
            return 101;
        case 'goland':
            return 102;
        case 'pycharm':
            return 103;
        case 'androidStudio':
            return 106;
        case 'clion':
            return 107;
        case 'phpstorm':
            return 108;
        default:
            return 0;
    }
};

export const getClientTypeFromIdeType = (ideType: Machine['ideType']): ICodingDesktopClientType => {
    switch (ideType) {
        case '0':
            return 'icoding';
        case '1':
        case '101':
        case '102':
        case '103':
        case '106':
        case '107':
        case '108':
            return 'icoding-projector';
        default:
            return 'aiIDE';
    }
};

// eslint-disable-next-line complexity
export const getClientTypeFromTemplateId = (templateId: number | string): ICodingDesktopClientType => {
    const castedTemplateId = typeof templateId === 'string' ? parseInt(templateId, 10) : templateId;
    switch (castedTemplateId) {
        case REAL_TEMPLATE_ID_MAP['idea-community-centos-7u5']:
        case REAL_TEMPLATE_ID_MAP['idea-community-ubuntu']:
        case REAL_TEMPLATE_ID_MAP['idea-ultimate-centos-7u5']:
        case REAL_TEMPLATE_ID_MAP['idea-ultimate-ubuntu']:
        case REAL_TEMPLATE_ID_MAP['pycharm-community-centos-7u5']:
        case REAL_TEMPLATE_ID_MAP['android-studio-community-centos-7u5']:
        case REAL_TEMPLATE_ID_MAP['goland-community-centos-7u5']:
            return 'icoding-projector';
        case REAL_TEMPLATE_ID_MAP.swan:
            return 'swan';
        case REAL_TEMPLATE_ID_MAP.aiIDE:
            return 'aiIDE';
        default:
            return 'icoding';
    }
};

export const getIsSharedByOthers = (resource: Workspace | WebIDEResource) => {
    return Boolean(resource.shareUname);
};

export const getLuoshuAccountSelectErrorObservable = (failureKind?: string) => {
    const errorMessage = (() => {
        switch (failureKind) {
            case FailureKind.accountCheckFailed:
                return '查询资源账户绑定状态失败，请刷新页面重试';
            case FailureKind.accountBindFailed:
                return '资源账户绑定失败，请刷新页面重试';
            case FailureKind.userCancelled:
                return '由于计费需要，创建代码空间前要绑定资源账户，请刷新页面重新选择绑定';
            default:
                return '';
        }
    })();
    return from(new Promise<Notification<ResourceStatusInfo>>(resolve => {
        resolve(new Notification<ResourceStatusInfo>(
            'E',
            undefined,
            new Error(errorMessage)
        ));
    }));
};
