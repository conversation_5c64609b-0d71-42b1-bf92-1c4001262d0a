/**
 * @file Web IDE首页
 */
import {useEffect, useMemo, useRef, useState} from 'react';
import {Flex, Radio, RadioChangeEvent, Space} from 'antd';
import {Loading} from '@/design/icode/Loading';
import {ErrorPage} from '@/design/icode/ErrorPages';
import {
    useLoading,
    loadResourceList,
    loadSharedWorkspaces,
    loadMyWorkspaces,
    loadSharedResourceList,
    setResourceCategory,
    setMachineCategory,
    useResourceCategory,
    useMachineCategory,
    useError,
    useShouldShowWelcomePage,
    loadWelcomePageContent,
    loadMyMachineList,
    loadShardMachineList,
} from '@/regions/icode/webIDE';
import ResourceList from '../ResourceList';
import MachineList from '../../IDEMachine/MachineList';
import ResourceShareModal from '../ResourceShareModal';
import Welcome from '../Welcome';
import AdvertisementBar from '../AdvertisementBar';
import ManagePortModal from '../ManagePortModal';
import RestartModal from '../ResourceCard/RestartModal';
import DeclineSharedResourceModal from '../DeclineSharedResourceModal';
import {DownloadAiIDEModal} from '../DownloadAiIDEModal';
import {Container, StyledTabs, RadioButton, Buffer, Warning} from './styled';

const onResourceCategoryChange = (e: RadioChangeEvent) => {
    setResourceCategory(e.target.value);
};

const onMachineCategoryChange = (e: RadioChangeEvent) => {
    setMachineCategory(e.target.value);
};

const items = [
    {
        label: '开发机',
        key: 'machine',
    },
    {
        label: '代码空间',
        key: 'codespace',
    },
];

const MainPage = () => {
    const loading = useLoading();
    const isFullyLoadedRef = useRef(false);
    const [activeKey, setActiveKey] = useState('machine');
    const error = useError();
    const resourceCategory = useResourceCategory();
    const machineCategory = useMachineCategory();

    // 初始化代码空间、开发机、工作区数据
    useEffect(
        () => {
            loadResourceList();
            loadSharedResourceList();
            loadMyMachineList({ideType: 999, page: 0, pageSize: 20});
            loadShardMachineList({ideType: 999});
            loadSharedWorkspaces();
            loadMyWorkspaces();
            loadWelcomePageContent();
        },
        []
    );

    useEffect(
        () => {
            if (!loading) {
                isFullyLoadedRef.current = true;
            }
        },
        [loading]
    );

    const body = useMemo(
        () => {
            if (error) {
                return <ErrorPage error={error} />;
            }
            return (
                <Container>
                    <Flex justify="space-between" align="center">
                        <StyledTabs activeKey={activeKey} onChange={setActiveKey} items={items} />
                        {
                            activeKey === 'machine'
                                ? (
                                    <Radio.Group
                                        defaultValue="mine"
                                        value={machineCategory}
                                        onChange={onMachineCategoryChange}
                                    >
                                        <RadioButton value="mine">我的开发机</RadioButton>
                                        <RadioButton value="shared">他人分享的开发机</RadioButton>
                                    </Radio.Group>
                                )
                                : (
                                    <Space>
                                        <Warning type="warning">一个月内从未使用的代码空间，平台将定时清理，请及时提交代码！</Warning>
                                        <Radio.Group
                                            defaultValue="mine"
                                            value={resourceCategory}
                                            onChange={onResourceCategoryChange}
                                        >
                                            <RadioButton value="mine">我的代码空间</RadioButton>
                                            <RadioButton value="shared">他人分享的代码空间</RadioButton>
                                        </Radio.Group>
                                    </Space>
                                )
                        }
                    </Flex>
                    <Buffer />
                    {
                        activeKey === 'machine' ? <MachineList /> : <ResourceList />
                    }
                </Container>
            );
        },
        [activeKey, error, machineCategory, resourceCategory]
    );

    const shouldShowWelcome = useShouldShowWelcomePage();

    if (loading && !isFullyLoadedRef.current) {
        return <Loading />;
    }
    if (shouldShowWelcome) {
        return <Welcome />;
    }
    return (
        <div>
            <AdvertisementBar />
            {body}
            <ResourceShareModal />
            <DeclineSharedResourceModal />
            <ManagePortModal />
            <RestartModal />
            <DownloadAiIDEModal />
        </div>
    );
};

export default MainPage;
