import {Radio, Tabs, Typography} from 'antd';
import styled from '@emotion/styled';
import {colors} from '@/constants/colors';

export const Container = styled.div`
    padding: 20px 0;
`;

export const RadioGroup = styled(Radio.Group)`
    margin-bottom: 20px !important;
`;

export const RadioButton = styled(Radio.Button)`
    min-width: 135px;
    text-align: center;
    padding: 0 8px;
`;

export const Buffer = styled.div`
    height: 32px;
`;

export const Warning = styled(Typography.Text)`
    margin-left: 16px;
    font-size: 12px;
`;

export const StyledTabs = styled(Tabs)`
    .ant-5-tabs-tab-active > .ant-5-tabs-tab-btn {
        color: ${colors.primary} !important;
    }
`;
