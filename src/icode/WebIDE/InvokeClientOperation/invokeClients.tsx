import {AIIDE_APP_SCHEME, ICODING_APP_SCHEME, ICODING_PROJECTOR_SCHEME} from '@/constants/icode/urls';
import {checkProtocol} from '@/utils/icode/invokeProtocol';

/**
 * @description 调起客户端，注入登录 token，并启动指定 workspace
 */
export function invokeClientWorkspace(token: string, host: string, path: string) {
    return checkProtocol(
        `${ICODING_APP_SCHEME}://baidu.icoding-remote/open-remote?token=${token}&host=${host}&path=${path}`
    );
}

export function invokeProjectorClient(wsUrl: string) {
    return checkProtocol(
        `${ICODING_PROJECTOR_SCHEME}://?wsUrl=${wsUrl}`
    );
}

export function invokeAiIDEClient(resourceId: string) {
    return checkProtocol(
        `${AIIDE_APP_SCHEME}://BaiduComate.internal-remote-development/connectCodespace?resourceId=${resourceId}`
    );
}
export function invokeAiIDEClientWidthHostname(hostname: string) {
    return checkProtocol(
        `${AIIDE_APP_SCHEME}://BaiduComate.internal-remote-development/connect?hostname=${hostname}`
    );
}

export function invokeAiIDEClientConnect() {
    return checkProtocol(
        `${AIIDE_APP_SCHEME}://BaiduComate.internal-remote-development/connect`
    );
}
