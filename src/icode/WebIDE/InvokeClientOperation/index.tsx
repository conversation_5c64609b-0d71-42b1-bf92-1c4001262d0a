/**
 * @file 调起 iCoding 客户端
 */
import {ReactNode, useCallback} from 'react';
import {Modal} from '@panda-design/components';
import {useBoolean} from 'huse';
import {ButtonStyle, ICodingDesktopClientType} from '@/types/icode/webIDE';
import {WORKSPACE_STATUS, RESOURCE_STATUS} from '@/constants/icode/webIDE';
import {
    setWorkspaceRestartInfo,
    showWorkspaceRestartModal,
} from '@/icode/IDEMachine/MachineList/WorkspaceRestartModal/region';
import {apiGetICodingUToken} from '@/api/icode/webIDE';
import {ICODING_HOST} from '@/constants/icode/urls';
import {setReconnectCommand, showReconnectModal} from '../../IDEMachine/MachineList/MachineReconnectModal/region';
import {getResourceTypeDisplayName, resolveResourceStatus} from '../utils';
import OperationButton, {OperationType} from '../OperationButton';
import {DownloadAiIDEModal} from '../DownloadAiIDEModal';
import {invokeAiIDEClient, invokeClientWorkspace, invokeProjectorClient} from './invokeClients';
import {DownloadModal} from './DownloadModal';

interface InvokeResourceBaseProps {
    mid?: string;
    path?: string;
    status: string;
    client: ICodingDesktopClientType;
    buttonStyle?: ButtonStyle;
    resourceId?: string; // 开发机打开
    ideaWsUrl?: string;
    icon?: ReactNode;
    tooltip?: string;
}

interface InvokeCodeSpaceProps extends InvokeResourceBaseProps {
    resourceType: 'codeSpace';
}

interface InvokeDevServerProps extends InvokeResourceBaseProps {
    resourceType: 'devServer';
    isAgentOffline: boolean;
    cmd: string;
    id: string;
}

const InvokeClientOperation = (props: InvokeCodeSpaceProps | InvokeDevServerProps) => {
    const {
        resourceType,
        status,
        client,
        buttonStyle,
        resourceId,
        ideaWsUrl,
        mid,
        path,
        icon,
        tooltip,
    } = props;

    const [downloadOpen, {on: showDownload, off: hideDownload}] = useBoolean();

    const invokeClient = useCallback(
        async () => {
            if (resourceType === 'devServer' && status === WORKSPACE_STATUS.offline) {
                const {isAgentOffline, cmd, id} = props;
                if (isAgentOffline) {
                    setReconnectCommand(cmd);
                    showReconnectModal();
                }
                else {
                    setWorkspaceRestartInfo({isAgentOffline, id, status, cmd});
                    showWorkspaceRestartModal();
                }
                return;
            }
            // 开发机或代码空间状态为运行中，可使用客户端调起
            if (status === WORKSPACE_STATUS.online || status === RESOURCE_STATUS.running) {
                if (client === 'icoding-projector') {
                    invokeProjectorClient(ideaWsUrl).catch(() => {
                        showDownload();
                    });
                }
                else if (client === 'icoding') {
                    const {utoken} = await apiGetICodingUToken();
                    const host = `${mid}.${ICODING_HOST}`;
                    invokeClientWorkspace(utoken, host, path).catch(() => {
                        showDownload();
                    });
                }
                if (client === 'aiIDE') {
                    invokeAiIDEClient(resourceId).catch(() => {
                        showDownload();
                    });
                }
                return;
            }
            const resourceTypeDisplayName = getResourceTypeDisplayName(resourceType);
            const {label: statusText} = resolveResourceStatus(status, resourceType);
            Modal.info({
                title: '提示',
                content: (
                    <div>
                        <p>当前{resourceTypeDisplayName}处于“{statusText}”状态，不支持运行</p>
                    </div>
                ),
            });
        },
        [resourceType, status, props, client, ideaWsUrl, showDownload, mid, path, resourceId]
    );

    return (
        <>
            <OperationButton
                icon={icon}
                type={OperationType.OpenInClient}
                buttonStyle={buttonStyle}
                tooltipMessage={tooltip ?? '客户端打开'}
                disabled={false}
                onClick={invokeClient}
            />
            {
                client === 'aiIDE' ? (
                    <DownloadAiIDEModal
                        open={downloadOpen}
                        onCancel={hideDownload}
                    />
                )
                    : (
                        <DownloadModal
                            client={client}
                            visible={downloadOpen}
                            onCancel={hideDownload}
                        />
                    )
            }
        </>
    );
};

export default InvokeClientOperation;
