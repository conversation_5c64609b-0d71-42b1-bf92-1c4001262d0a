import {MouseEvent, ReactNode, useMemo} from 'react';
import {debounce, noop} from 'lodash';
import {IconIDEClient, IconRefresh} from '@baidu/ee-icon';
import {Button} from '@panda-design/components';
import {css} from '@emotion/css';
import {ButtonStyle} from '@/types/icode/webIDE';
import iconMap from '@/icons-icode/webIDE';

const buttonCss = css`
    width: 32px;
    height: 24px;
`;

export enum OperationType {
    Play = 'play',
    Pause = 'pause',
    Delete = 'delete',
    Restart = 'restart',
    OpenInWeb = 'openInWeb',
    OpenInClient = 'openInClient',
    StopShare = 'stopShare',
    Share = 'share',
    More = 'more',
    ShowDetail = 'show',
}

function getIcon(type: OperationType) {
    switch (type) {
        case OperationType.OpenInClient:
            return IconIDEClient;
        case OperationType.Restart:
            return IconRefresh;
        default:
            return iconMap[type] ?? iconMap.more;
    }
}

interface Props {
    buttonStyle?: ButtonStyle;
    type: OperationType;
    disabled?: boolean;
    onClick?: (e: MouseEvent) => void;
    tooltipMessage?: string;
    debounced?: boolean;
    onMouseEnter?: () => void;
    onMouseLeave?: () => void;
    onFocus?: () => void;
    icon?: ReactNode;
}

const OperationButton = (props: Props) => {
    const {
        buttonStyle,
        type,
        disabled = false,
        onClick = noop,
        tooltipMessage,
        debounced = false,
        onMouseEnter,
        onMouseLeave,
        onFocus,
        icon,
    } = props;

    const handleClick = useMemo(
        () => (debounced ? debounce(onClick, 300) : onClick),
        [debounced, onClick]
    );

    const Icon = getIcon(type);

    return (
        <Button
            type={buttonStyle === 'bordered' ? 'default' : 'text'}
            className={buttonCss}
            onClick={handleClick}
            disabled={disabled}
            onMouseEnter={onMouseEnter}
            onMouseLeave={onMouseLeave}
            onFocus={onFocus}
            tooltip={tooltipMessage}
            icon={icon ?? <Icon />}
        />
    );
};

export default OperationButton;
