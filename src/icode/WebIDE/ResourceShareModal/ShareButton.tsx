import {ReactNode} from 'react';
import {ButtonStyle, ResourceShareType} from '@/types/icode/webIDE';
import {getResourceShareTypeDisplayName} from '../utils';
import OperationButton, {OperationType} from '../OperationButton';

interface Props {
    kind: ResourceShareType;
    buttonStyle?: ButtonStyle;
    onClick: () => void;
    icon?: ReactNode;
}

const ShareButton = ({kind, icon, onClick, buttonStyle}: Props) => {
    const resourceTypeDisplayName = getResourceShareTypeDisplayName(kind);
    const toolTip = '分享' + resourceTypeDisplayName;

    return (
        <OperationButton
            type={OperationType.Share}
            buttonStyle={buttonStyle}
            tooltipMessage={toolTip}
            onClick={onClick}
            icon={icon}
        />
    );
};

export default ShareButton;
