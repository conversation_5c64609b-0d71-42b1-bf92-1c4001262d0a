import {SubTitle} from '@/design/icode/SubTitle';
import {FieldsContainer} from '@/design/icode/FormElements';
import Zone from './Zone';
import LuoshuAccount from './LuoshuAccount';
import MachineName from './MachineName';
import ComboConfig from './ComboConfig';
import CloudStorage from './CloudStorage';
import PublicImage from './PublicImage';
import AccountName from './AccountName';
import AdminPassword from './AdminPassword';

const Fields = () => {

    return (
        <FieldsContainer>
            <SubTitle title="集群信息" />
            <LuoshuAccount />
            <Zone />
            <SubTitle title="配置信息" />
            <MachineName />
            <ComboConfig />
            <CloudStorage />
            <PublicImage />
            <SubTitle title="系统信息" />
            <AccountName />
            <AdminPassword />
        </FieldsContainer>
    );
};

export default Fields;
