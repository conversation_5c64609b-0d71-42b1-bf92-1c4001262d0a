import {useMemo, useEffect, useRef} from 'react';
import {
    loadResourceList,
    loadSharedResourceList,
    OriginCategory,
    useResourceCategory,
    useResourceList,
    useResourceListLoading,
    useSharedResourceList,
    useSharedResourceListLoading,
} from '@/regions/icode/webIDE';
import {RESOURCE_STATUS} from '@/constants/icode/webIDE';
import {Timer} from '@/types/icode/common';
import CardView from './CardView';

const refreshResources = (category: OriginCategory) => {
    if (category === 'shared') {
        loadSharedResourceList();
    }
    else if (category === 'mine') {
        loadResourceList();
    }
};

const useResources = () => {
    const category = useResourceCategory();
    const myResources = useResourceList();
    const sharedResources = useSharedResourceList();
    const resources = useMemo(
        () => {
            switch (category) {
                case 'mine':
                    return myResources ?? [];
                case 'shared':
                    return sharedResources ?? [];
                default:
                    return [];
            }
        },
        [category, myResources, sharedResources]
    );
    const timerRef = useRef<Timer>(null);

    useEffect(
        () => {
            const isAnyCodeSpaceBooting = resources?.some(item => item.status === RESOURCE_STATUS.booting);
            if (isAnyCodeSpaceBooting) {
                if (timerRef.current === null) {
                    timerRef.current = setInterval(
                        () => refreshResources(category),
                        4 * 1000
                    );
                }
            }
            else {
                timerRef.current && clearInterval(timerRef.current);
                timerRef.current = null;
            }
            return () => {
                timerRef.current && clearInterval(timerRef.current);
                timerRef.current = null;
            };
        },
        [category, resources]
    );

    const isMyResourcesLoading = useResourceListLoading();
    const isSharedResourcesLoading = useSharedResourceListLoading();
    return {
        data: resources,
        loading: category === 'mine' ? isMyResourcesLoading : isSharedResourcesLoading,
    };
};

const ResourceList = () => {
    const {data} = useResources();

    return <CardView dataSource={data} />;
};

export default ResourceList;
