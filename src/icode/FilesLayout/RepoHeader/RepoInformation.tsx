/**
 * @file 代码库侧边栏
 */

import {Tooltip, Space} from 'antd';
import {IconLockCloseSolid} from '@baidu/ee-icon';
import styled from '@emotion/styled';
import {css} from '@emotion/css';
import {fontSize, marginLeft, message, Tag} from '@panda-design/components';
import {useEffect, useMemo, useState, MouseEvent} from 'react';
import RepoLogo from '@/design/icode/RepoLogo';
import {CopyButton} from '@/design/icode/Helpful/CopyButton';
import {useCurrentRepo} from '@/regions/icode/currentRepo';
import {useCurrentUserName} from '@/regions/icode/currentUser';
import {apiPostGrayTraffic} from '@/api/icode/gray';
import {APP_IS_ONLINE_PRODUCTION} from '@/constants/app';
import {getRepoProduct} from '@/utils/icode/repo';
import {ProductLink} from '@/links/icode/routes';
import {FrozenStatus} from '@/types/icode/repo';
import useRepoFrozenStatus from '@/hooks/icode/repoSettings/useRepoFrozenStatus';
import {REPO_FROZEN_STATUS} from '@/constants/icode/repo';
import {createLink} from '@/links/createLink';
import {RepoLanguageGrid} from './RepoLanguageGrid';

const logoCss = css`
    position: relative;
    margin-right: 10px;
`;

const TitleContainer = styled.div`
    display: flex;
    height: 32px;
    align-items: center;
`;

const TitleAndLanguage = styled.div`
    display: flex;
    flex-direction: column;
    gap: 4px;
`;

const splitCss = css`
    color: var(--color-gray-6);
    font-size: 16px;
    margin: 0 4px;
`;

const privateIconCss = css`
    position: relative;
    top: 1px;
`;

const deepWikiStyledLinkClass = css`
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 30%, #ff6b6b 70%, #ff4757 100%);
    color: white;
    font-weight: 700;
    font-size: 14px;
    height: 32px;
    border-radius: 16px;
    padding: 0 16px;
    box-shadow: 0 4px 16px rgba(255, 107, 107, 0.4), 0 2px 8px rgba(255, 71, 87, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin-left: 8px;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    cursor: pointer;
    z-index: 1;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s ease;
        pointer-events: none;
        z-index: -1;
    }

    &:hover {
        background: linear-gradient(135deg, #ff5252 0%, #ff7043 30%, #ff5252 70%, #ff3742 100%);
        box-shadow: 0 6px 24px rgba(255, 107, 107, 0.5), 0 4px 12px rgba(255, 71, 87, 0.3);
        transform: translateY(-2px) scale(1.02);
        color: white;
        text-decoration: none;

        &::before {
            left: 100%;
        }
    }

    &:active {
        transform: translateY(-1px) scale(1.01);
        box-shadow: 0 4px 16px rgba(255, 107, 107, 0.4);
    }

    &:focus, &:visited {
        color: white;
        text-decoration: none;
    }

    /* 添加发光效果 */
    &::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(135deg, #ff6b6b, #ff8e53, #ff6b6b, #ff4757);
        border-radius: 18px;
        z-index: -2;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
    }

    &:hover::after {
        opacity: 0.3;
        animation: glow 2s ease-in-out infinite alternate;
    }

    @keyframes glow {
        from { opacity: 0.3; }
        to { opacity: 0.5; }
    }
`;

// DeepWiki链接定义
interface ParamsDeepWikiLink {
    repo: string;
    username: string;
}

const DeepWikiLinkComponent = createLink<ParamsDeepWikiLink>(
    APP_IS_ONLINE_PRODUCTION
        ? 'http://icode.baidu.com.cn/?repo={repo}&username={username}'
        : 'http://icode.baidu.com.cn:8011/?repo={repo}&username={username}'
);

const RepoInformation = () => {
    const repo = useCurrentRepo();
    const repoName = repo.name || '';
    const productName = getRepoProduct(repo.fullName);
    const frozenStatus = useRepoFrozenStatus();
    const currentUserName = useCurrentUserName();
    const [isShowDeepWiki, setIsShowDeepWiki] = useState(false);

    // 检查用户是否在灰度流量中
    useEffect(
        () => {
            const checkGrayTraffic = async () => {
                if (currentUserName) {
                    try {
                        const result = await apiPostGrayTraffic({
                            username: currentUserName,
                            feature: 'deepwikiEntryFeature',
                        });
                        setIsShowDeepWiki(result.inGrayTraffic);
                    }
                    catch (error) {
                        message.info('检查灰度流量失败');
                        setIsShowDeepWiki(false);
                    }
                }
            };
            checkGrayTraffic();
        },
        [currentUserName]
    );

    /**
     * 使用useMemo构建DeepWiki链接地址
     */
    const deepWikiHref = useMemo(
        () => {
            if (repo?.fullName && currentUserName) {
                return DeepWikiLinkComponent.toUrl({
                    repo: repo?.fullName,
                    username: currentUserName,
                });
            }
            return '#';
        },
        [repo?.fullName, currentUserName]
    );

    /**
     * 处理DeepWiki链接点击事件
     */
    const handleDeepWikiClick = (event: MouseEvent<HTMLAnchorElement>) => {
        if (deepWikiHref === '#') {
            event.preventDefault();
            message.info('无法生成DeepWiki链接，请检查仓库信息和用户信息');

        }
    };

    return (
        <>
            <RepoLogo size={36} name={repoName} className={logoCss} />
            <TitleAndLanguage>
                <TitleContainer>
                    {productName && (
                        <>
                            <Tooltip overlay="代码库目录，可批量管理权限">
                                <span className={fontSize(16)}>
                                    <ProductLink name={productName}>{productName}</ProductLink>
                                </span>
                            </Tooltip>
                            <div className={splitCss}>/</div>
                        </>
                    )}
                    <span className={fontSize(16)}>{repoName}</span>
                    {(frozenStatus && frozenStatus !== FrozenStatus.ACTIVE) && (
                        <Tag type="flat">{REPO_FROZEN_STATUS[frozenStatus]}</Tag>
                    )}

                    <Space className={marginLeft(10)}>
                        {repo.secret === 'PRIVATE' && <IconLockCloseSolid className={privateIconCss} />}
                        {repo.fullName && <CopyButton text={repo.fullName} className={fontSize(16)} />}
                        {repo.fullName && currentUserName && isShowDeepWiki && (
                            <Tooltip title="在DeepWiki中探索代码知识图谱">
                                <DeepWikiLinkComponent
                                    repo={repo.fullName}
                                    username={currentUserName}
                                    blank
                                    onClick={handleDeepWikiClick}
                                    forceHtmlAnchor
                                    className={deepWikiStyledLinkClass}
                                >
                                    🧠 DeepWiki
                                </DeepWikiLinkComponent>
                            </Tooltip>
                        )}

                    </Space>
                </TitleContainer>
                <RepoLanguageGrid repoName={repo.fullName} />
            </TitleAndLanguage>
        </>
    );
};

export default RepoInformation;
