import {useCallback, useMemo} from 'react';
import {message} from '@panda-design/components';
import {noop, partial} from 'lodash';
import {Workspace} from '@/api/icode/webIDE';
import {ButtonStyle} from '@/types/icode/webIDE';
import {IconAiIde} from '@/icons-icode/product';
import InvokeClientOperation from '../../../WebIDE/InvokeClientOperation';
import DeclineSharedWorkspace from '../DeclineSharedWorkspace';
import {
    hideWorkspaceDeleteModal,
    setWorkspaceDeleteInfo,
    showWorkspaceDeleteModal,
} from '../WorkspaceDeleteModal/region';
import OperationButton, {OperationType} from '../../../WebIDE/OperationButton';
import {hideResourceShareModal, showResourceShareModal} from '../../../WebIDE/ResourceShareModal/region';
import ShareButton from '../../../WebIDE/ResourceShareModal/ShareButton';
import {getIsSharedByOthers} from '../../../WebIDE/utils';
import {OperationButtonGroup} from '../../../WebIDE/styled';
import {checkDummyWorkspace} from '../utils';
import {getWorkspaceJumpUrl} from '../openWorkspace';
import {pushModal} from '../useModalStackInfo';
import icoding from '../assets/icoding.png';
import icodingProject from '../assets/icoding-project.png';

interface Props {
    workspace: Workspace;
    buttonStyle?: ButtonStyle;
    supportVscode: boolean;
    supportJetbrains: boolean;
}

export const WorkspaceOperations = ({workspace, buttonStyle, supportVscode, supportJetbrains}: Props) => {
    const path = useMemo(
        () => {
            if (!workspace.path) {
                return '';
            }
            return decodeURIComponent(workspace.path).replace(/^vscode-remote:\/\/.+:(\d+)/g, '');
        },
        [workspace.path]
    );

    const handleShareClick = useCallback(
        () => {
            const show = partial(showResourceShareModal, {
                kind: 'workspace',
                workspaceId: workspace.id,
                name: workspace.editionPath || workspace.name,
                hostname: workspace.hostname,
                editionName: workspace.editionName || '',
                jumpUrl: getWorkspaceJumpUrl(workspace),
            });
            show();
            pushModal(show, hideResourceShareModal);
        },
        [workspace]
    );

    const isSharedByOthers = getIsSharedByOthers(workspace);

    const isShareable = !isSharedByOthers;

    const isDummyWorkspace = checkDummyWorkspace(workspace);

    const disableDelete = isSharedByOthers || isDummyWorkspace;

    const handleWorkspaceDelete = useCallback(
        () => {
            if (disableDelete) {
                message.error('对不起，您没有权限对该工作区进行删除操作！', 5);
                return;
            }
            setWorkspaceDeleteInfo({workspace});
            showWorkspaceDeleteModal();
            pushModal(showWorkspaceDeleteModal, hideWorkspaceDeleteModal);
        },
        [disableDelete, workspace]
    );

    return (
        <OperationButtonGroup>
            <OperationButton
                type={OperationType.Delete}
                buttonStyle="borderless"
                disabled={disableDelete}
                onClick={disableDelete ? noop : handleWorkspaceDelete}
                tooltipMessage={isSharedByOthers ? '无操作权限' : '删除工作区'}
            />
            <InvokeClientOperation
                tooltip="Comate AI IDE 打开"
                icon={<IconAiIde />}
                mid={workspace.machineId}
                path={path}
                isAgentOffline={workspace.isAgentOffline}
                cmd={workspace.cmd}
                status={workspace.status}
                id={workspace.id}
                resourceType="devServer"
                client="aiIDE"
                ideaWsUrl={workspace.panDomain}
            />
            {supportVscode && (
                <InvokeClientOperation
                    tooltip="iCoding for VS Code打开"
                    icon={<img src={icoding} style={{width: 16}} />}
                    mid={workspace.machineId}
                    path={path}
                    isAgentOffline={workspace.isAgentOffline}
                    cmd={workspace.cmd}
                    status={workspace.status}
                    id={workspace.id}
                    resourceType="devServer"
                    client="icoding"
                    ideaWsUrl={workspace.panDomain}
                />
            )}
            {supportJetbrains && (
                <InvokeClientOperation
                    tooltip="iCoding Projector打开"
                    icon={<img src={icodingProject} style={{width: 16}} />}
                    mid={workspace.machineId}
                    path={path}
                    isAgentOffline={workspace.isAgentOffline}
                    cmd={workspace.cmd}
                    status={workspace.status}
                    id={workspace.id}
                    resourceType="devServer"
                    client="icoding-projector"
                    ideaWsUrl={workspace.panDomain}
                />
            )}
            {isShareable && <ShareButton kind="workspace" onClick={handleShareClick} buttonStyle="borderless" />}
            {isSharedByOthers && <DeclineSharedWorkspace workspaceId={workspace.id} buttonStyle={buttonStyle} />}
        </OperationButtonGroup>
    );
};
