import {Table} from 'antd';
import {useCallback} from 'react';
import {Modal} from '@panda-design/components';
import {IconInfoCircleSolid} from '@baidu/ee-icon';
import {Workspace} from '@/api/icode/webIDE';
import Notice from '@/design/icode/Notice';
import {useStackedModalProps, exitStackMode} from '../useModalStackInfo';
import getWorkspaceColumns from './workspaceColumns';

interface Props {
    workspaces: Workspace[];
    visible: boolean;
    onCancel: () => void;
    supportVscode: boolean;
    supportJetbrains: boolean;
}

const WorkspacesManageModal = ({workspaces, visible, onCancel, supportVscode, supportJetbrains}: Props) => {
    const extraProps = useStackedModalProps({isBase: true, hasFooter: false});

    const handleCancel = useCallback(
        () => {
            onCancel();
            exitStackMode();
        },
        [onCancel]
    );

    return (
        <Modal
            title="全部工作区"
            open={visible}
            onCancel={handleCancel}
            footer={null}
            {...extraProps}
        >
            <Notice type="info" closable={false} icon={<IconInfoCircleSolid />}>
                iCoding-VS Code和Jetbrains系列IDE进入维护状态，推荐您使用AI IDE
            </Notice>
            <Table
                style={{marginTop: 16}}
                dataSource={workspaces}
                pagination={{pageSize: 5, hideOnSinglePage: true}}
                rowKey="id"
                columns={getWorkspaceColumns({supportVscode, supportJetbrains})}
            />
        </Modal>
    );
};

export default WorkspacesManageModal;
