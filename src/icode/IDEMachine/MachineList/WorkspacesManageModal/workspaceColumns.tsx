import {TableColumnsType} from 'antd';
import {message} from '@panda-design/components';
import {isEmpty} from 'lodash';
import {css} from '@emotion/css';
import {apiGetToggleTopWorkspace, Workspace} from '@/api/icode/webIDE';
import {loadMyWorkspaces} from '@/regions/icode/webIDE';
import {interactive} from '@/styles/icode/componentStyle';
import topIconCss from '@/assets/icode/webide2/topIcon.png';
import notTopIconCss from '@/assets/icode/webide2/notTopIcon.png';
import {checkDummyWorkspace} from '../utils';
import openWorkspace from '../openWorkspace';
import {WorkspaceOperations} from './WorkspaceOperations';
import {ShareIcon} from './ShareIcon';

const toggleTop = (record: Workspace) => {
    // 切换置顶状态，1置顶，0取消置顶。
    // 0 ==toggle=> 1 | 1 ==toggle=> 0
    const type = 1 - record.isTop as 0 | 1;
    apiGetToggleTopWorkspace({workspaceId: record.id, type}).then(() => {
        loadMyWorkspaces();
    }).catch(e => {
        message.error(e.message, 5);
    });
};

interface Params {
    supportVscode: boolean;
    supportJetbrains: boolean;
}

const getWorkspaceColumns: (params: Params) => TableColumnsType<Workspace> = ({supportVscode, supportJetbrains}) => [
    {
        title: '',
        key: 'top',
        width: 36,
        render(_, record: Workspace) {
            const isTop = Boolean(record.isTop);
            const isDummyWorkspace = checkDummyWorkspace(record);
            if (isDummyWorkspace) {
                return null;
            }
            const iconCss = css`
                height: 16px;
                width: 16px;
                background-image: url(${isTop ? topIconCss : notTopIconCss});
                background-size: 100%;
                cursor: pointer;
            `;
            return (
                <>
                    {record.isSharing && <ShareIcon />}
                    <div
                        className={iconCss}
                        onClick={() => toggleTop(record)}
                    >
                    </div>
                </>
            );
        },
    },
    {
        title: '工作区名称',
        dataIndex: 'editionName',
        ellipsis: true,
        render(_, record: Workspace) {
            return (
                <span className={interactive} onClick={() => openWorkspace(record)}>{record.editionName}</span>
            );
        },
    },
    {
        title: '目录',
        dataIndex: 'editionPath',
        ellipsis: true,
        render(_, record: Workspace) {
            return isEmpty(record.editionPath) ? '---' : record.editionPath;
        },
    },
    {
        title: '操作',
        ellipsis: true,
        render(_, record: Workspace) {
            return (
                <WorkspaceOperations
                    supportVscode={supportVscode}
                    supportJetbrains={supportJetbrains}
                    workspace={record}
                />
            );
        },
    },
];

export default getWorkspaceColumns;
