import {Modal} from '@panda-design/components';
import CopyableInput from '@/design/icode/CopyableInput';
import {hideReconnectModal, useReconnectCommand, useReconnectModalVisibility} from './region';

const MachineReconnectModal = () => {
    const visible = useReconnectModalVisibility();
    const command = useReconnectCommand();
    if (!visible) {
        return null;
    }

    return (
        <Modal
            title="重启"
            open={visible}
            onOk={hideReconnectModal}
            onCancel={hideReconnectModal}
        >
            <CopyableInput title="与开发机断开联系。请在此开发机中重新执行：" content={command} />
        </Modal>
    );
};

export default MachineReconnectModal;
