import {useCallback, useEffect, useMemo, useRef} from 'react';
import {Flex, Pagination} from 'antd';
import {
    loadSharedWorkspaces,
    loadMyWorkspaces,
    OriginCategory,
    useMachineCategory,
    useMyWorkspaces,
    useSharedWorkspaces,
    useMyMachineListParam,
    useMyMachineList,
    loadMyMachineList,
    loadShardMachineList,
    useShardMachineList,
    setMyMachineListParam,
} from '@/regions/icode/webIDE';
import {WORKSPACE_STATUS} from '@/constants/icode/webIDE';
import {Timer} from '@/types/icode/common';
import {MachineNew, ParamsOfGetMachines} from '@/api/icode/webIDE';
import MachineReconnectModal from './MachineReconnectModal';
import WorkspaceRestartModal from './WorkspaceRestartModal';
import WorkspaceDeleteModal from './WorkspaceDeleteModal';
import MachineDeleteModal from './MachineDeleteModal';
import MachineAddSelectModal from './MachineAddSelectModal';
import CardView from './CardView';


const useMachines = () => {
    const category = useMachineCategory();
    const params = useMyMachineListParam();
    const {machines, totalCount} = useMyMachineList();
    const sharedMachines = useShardMachineList();
    const myWorkspaces = useMyWorkspaces();
    const sharedWorkspaces = useSharedWorkspaces();
    const timerRef = useRef<Timer>(null);
    const refreshMachines = useCallback(
        (category: OriginCategory) => {
            if (category === 'shared') {
                loadShardMachineList({ideType: 999});
                loadSharedWorkspaces();
            }
            else if (category === 'mine') {
                loadMyMachineList(params as ParamsOfGetMachines);
                loadMyWorkspaces();
            }
        },
        [params]
    );
    const machinesOnDisplay = useMemo(
        () => {
            if (category === 'mine') {
                return machines ?? [];
            }
            if (category === 'shared') {
                return sharedMachines ?? [];
            }
            return [];
        },
        [category, machines, sharedMachines]
    );


    const workspacesOnDisplay = useMemo(
        () => {
            if (category === 'mine') {
                return myWorkspaces ?? [];
            }
            if (category === 'shared') {
                return sharedWorkspaces ?? [];
            }
            return [];
        },
        [category, myWorkspaces, sharedWorkspaces]
    );

    useEffect(
        () => {
            const isAnyAgentRebooting = workspacesOnDisplay.some(item => {
                return item.status === WORKSPACE_STATUS.rebooting;
            });
            if (isAnyAgentRebooting) {
                if (timerRef.current === null) {
                    timerRef.current = setInterval(
                        () => refreshMachines(category),
                        4 * 1000
                    );
                }
            }
            else {
                timerRef.current && clearInterval(timerRef.current);
                timerRef.current = null;
            }
            return () => {
                timerRef.current && clearInterval(timerRef.current);
                timerRef.current = null;
            };
        },
        [workspacesOnDisplay, category, refreshMachines]
    );

    return {machines: machinesOnDisplay, totalCount, workspaces: workspacesOnDisplay};
};

const MachineList = () => {
    const {machines, workspaces, totalCount} = useMachines();
    const params = useMyMachineListParam();

    useEffect(
        () => {
            loadMyMachineList(params);
        },
        [params]
    );
    const {page, pageSize} = params;
    const handlePageChange = useCallback(
        (page: number, pageSize: number) => {
            setMyMachineListParam({page: page - 1, pageSize});
        },
        []
    );


    return (
        <>
            <CardView machines={machines as MachineNew[]} workspaces={workspaces} />
            <Flex justify="flex-end">
                <Pagination
                    hideOnSinglePage
                    style={{marginTop: 24}}
                    pageSize={pageSize}
                    total={totalCount}
                    current={page + 1}
                    onChange={handlePageChange}
                />
            </Flex>
            <MachineReconnectModal />
            <MachineDeleteModal />
            <MachineAddSelectModal />
            <WorkspaceRestartModal />
            <WorkspaceDeleteModal />
        </>
    );
};

export default MachineList;
