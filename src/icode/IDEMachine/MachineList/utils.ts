import {maxBy} from 'lodash';
import {Workspace, IDETypeString} from '@/api/icode/webIDE';
import {IDE_PRODUCT, WORKSPACE_STATUS} from '@/constants/icode/webIDE';
import {IdeType} from '@/types/icode/webIDE';

function dateStringToNumber(date: string): number {
    return Date.parse(date.replace(/-/g, '/'));
}

// 对工作区数组进行时间顺序排序，越接近现在的排在前面
export function workspaceModifiedTimeDescending(a: Workspace, b: Workspace) {
    return dateStringToNumber(b.mtime) - dateStringToNumber(a.mtime);
}

// isTop为1的工作区排在前面
export function workspacePinned(a: Workspace, b: Workspace) {
    return b.isTop - a.isTop;
}

export function checkDummyWorkspace(workspace: Workspace): boolean {
    return workspace?.editionName === '新工作区';
}

// 找到属于某个机器的工作区
export function getWorkspacesOfMachine(mid: string, workspaces: Workspace[]) {
    return workspaces.filter(item => item.machineId === mid);
}

// 找到最近一次使用的workspace
export function getLastUsedWorkspace(workspaces: Workspace[]) {
    return maxBy(workspaces, item => dateStringToNumber(item.mtime));
}

export function handleStatusType(value: string | undefined): 'success' | 'error' | 'pending' | 'info' {
    const code = value && String(value);
    switch (code) {
        case WORKSPACE_STATUS.online:
            return 'success';
        case WORKSPACE_STATUS.offline:
            return 'error';
        case WORKSPACE_STATUS.rebooting:
            return 'pending';
        default:
            return 'info';
    }
}

export function handleStatusText(value: string | undefined) {
    const code = value && String(value);
    switch (code) {
        case WORKSPACE_STATUS.online:
            return '在线';
        case WORKSPACE_STATUS.offline:
            return '离线';
        case WORKSPACE_STATUS.rebooting:
            return '重启中';
        default:
            return '未知';
    }
}

export function convertToIdentifiableIdeType(ideTypeIndex: IDETypeString): IdeType {
    switch (ideTypeIndex) {
        case IDE_PRODUCT.vscode:
        case IDE_PRODUCT.vscodeWindows:
            return 'vscode';
        case IDE_PRODUCT.ideaCommunity:
            return 'ideaCommunity';
        case IDE_PRODUCT.ideaUltimate:
            return 'ideaUltimate';
        case IDE_PRODUCT.goland:
            return 'goland';
        case IDE_PRODUCT.pycharm:
            return 'pycharm';
        case IDE_PRODUCT.clion:
            return 'clion';
        case IDE_PRODUCT.phpstorm:
            return 'phpstorm';
        case IDE_PRODUCT.androidStudio:
            return 'androidStudio';
        default:
            return 'vscode';
    }
}

export function getWorkspacesSorted(workspaces: Workspace[]): Workspace[] {
    const dummyWorkspace = workspaces.filter(checkDummyWorkspace);
    const workspacesWithoutDummy = workspaces.filter(item => !checkDummyWorkspace(item));
    const sortedWorkspaces = [...workspacesWithoutDummy].sort(workspaceModifiedTimeDescending).sort(workspacePinned);
    return [...(dummyWorkspace ? dummyWorkspace : []), ...sortedWorkspaces];
}
