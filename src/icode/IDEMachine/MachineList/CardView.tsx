/**
 * @file 开发机卡片列表视图，用于存放拖拽逻辑
 */

import {useMemo} from 'react';
import {Machine, MachineNew, Workspace} from '@/api/icode/webIDE';
import DraggableGrid from '@/design/icode/DraggableGrid';
import {useMachineCategory} from '@/regions/icode/webIDE';
import CardAdd from '@/design/icode/Card/CardAdd';
import {position} from '@/styles/icode/classNames';
import NoMachine from './NoMachine';
import {showMachineAddSelectModal} from './MachineAddSelectModal/region';
import {getWorkspacesOfMachine} from './utils';
import MachineCard from './MachineCard';

interface ItemData {
    id: string;
    machine: Omit<MachineNew, 'jetbrains' | 'vscode'>;
    jetbrainsList: Array<Machine & {workspaces: Workspace[]}>;
    vscodeList: Array<Machine & {workspaces: Workspace[]}>;
}

interface Props{
    itemData: ItemData;
}

const SortableItem = ({itemData}: Props) => {
    const {machine, jetbrainsList, vscodeList} = itemData;
    return (
        <MachineCard
            machine={machine}
            jetbrainsList={jetbrainsList}
            vscodeList={vscodeList}
        />
    );
};

interface CardViewProps {
    machines: MachineNew[];
    workspaces: Workspace[];
}

const CardView = ({machines, workspaces}: CardViewProps) => {
    const dataSource = useMemo<ItemData[]>(
        () => machines.map(machine => {
            const {jetbrains, vscode, ...rest} = machine;
            return {
                id: rest.ip,
                machine: rest,
                vscodeList: vscode?.map(
                    ({id, machineId, ...other}) => ({
                        id,
                        machineId,
                        ...other,
                        workspaces: getWorkspacesOfMachine(id || machineId, workspaces),
                    })
                ),
                jetbrainsList: jetbrains?.map(
                    ({id, machineId, ...other}) => ({
                        id,
                        machineId,
                        ...other,
                        workspaces: getWorkspacesOfMachine(id || machineId, workspaces),
                    })
                ),
            };
        }),
        [machines, workspaces]
    );

    const category = useMachineCategory();

    if (machines.length === 0) {
        return <NoMachine />;
    }

    const showAdd = category === 'mine';

    return (
        <div className={position('relative')}>
            <DraggableGrid<ItemData>
                storageKey={`machineGridOrder/${category}`}
                dataSource={dataSource}
                SortableItemComponent={SortableItem}
                leaveFirstCellBlank={showAdd}
            />
            {showAdd && <CardAdd title="添加开发机" onClick={showMachineAddSelectModal} />}
        </div>
    );
};

export default CardView;
