import {ReactNode, useCallback, useMemo} from 'react';
import {Button} from '@panda-design/components';
import {Popover} from 'antd';
import styled from '@emotion/styled';
import {useBoolean} from 'huse';
import {marginLeft} from '@panda-design/components';
import {Workspace} from '@/api/icode/webIDE';
import {ButtonStyle} from '@/types/icode/webIDE';
import useRestartWorkspaceHandler from '../useRestartWorkspaceHandler';
import OperationButton, {OperationType} from '../../../WebIDE/OperationButton';

export const RestartConfirmGroup = styled.div`
    margin-top: 12px;
    display: flex;
    justify-content: flex-end;
`;

interface Props {
    buttonStyle: ButtonStyle;
    disabled: boolean;
    workspace?: Workspace;
    icon?: ReactNode;
}

const RestartButton = ({buttonStyle, icon, disabled, workspace}: Props) => {
    const [visible, {off: hidePopover, toggle}] = useBoolean(false);

    const restart = useRestartWorkspaceHandler(workspace);

    const handleConfirm = useCallback(
        () => {
            restart();
            hidePopover();
        },
        [hidePopover, restart]
    );

    const restartAgentPopoverContent = useMemo(
        () => (
            <>
                <div>确认重启 iCoding 服务？</div>
                <RestartConfirmGroup>
                    <Button size="small" onClick={hidePopover}>否</Button>
                    <Button size="small" type="primary" className={marginLeft(4)} onClick={handleConfirm}>是</Button>
                </RestartConfirmGroup>
            </>
        ),
        [hidePopover, handleConfirm]
    );

    return (
        <Popover
            content={restartAgentPopoverContent}
            open={visible && !disabled}
            trigger="click"
            onOpenChange={toggle}
        >
            <OperationButton
                icon={icon}
                type={OperationType.Restart}
                buttonStyle={buttonStyle}
                disabled={disabled}
                tooltipMessage="重启iCoding"
            />
        </Popover>
    );
};

export default RestartButton;
