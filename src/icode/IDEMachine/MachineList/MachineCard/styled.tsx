import styled from '@emotion/styled';

const Label = styled.div`
    margin-right: 4px;
    width: 33%;

    &:last-of-type {
        margin-right: 0;
    }
`;

export const WorkspaceLabel = styled(Label)`
    overflow: hidden;
    padding: 0 8px;
    line-height: 24px;
    background-color: var(--color-gray-3);
    cursor: pointer;
`;

export const Tip = styled.div`
    line-height: 18px;
    font-size: 12px;
    color: var(--color-gray-7);
`;

export const WorkspaceLabelRow = styled.div`
    margin-top: 4px;
    color: var(--color-gray-9);
    font-size: 12px;
    display: flex;
    align-items: center;
`;

export const CheckAll = styled(Label)`
    padding-left: 4px;
    line-height: 24px;
    color: var(--color-gray-7);
`;
