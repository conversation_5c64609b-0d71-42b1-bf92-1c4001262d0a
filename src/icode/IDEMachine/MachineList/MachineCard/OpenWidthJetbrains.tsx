import {Button, Modal} from '@panda-design/components';
import {useBoolean} from 'huse';
import {Flex} from 'antd';
import {useEffect} from 'react';
import {Machine, Workspace} from '@/api/icode/webIDE';
import InstallIdeGuide from '@/icode/WebIDE/MachineNew/InstallIdeGuide';
import jetbrains from '../assets/jetbrains.png';
import StatusHeadWidthShare from './StatusHeadWidthShare';
import IdeWorkSpaceItem from './IdeWorkspaceItem';

interface Props {
    jetbrainsList: Array<Machine & {workspaces: Workspace[]}>;
}

const OpenWidthJetbrains = ({jetbrainsList}: Props) => {
    const [open, {on: show, off: hide}] = useBoolean();
    const [showInstall, {on: displayInstall, off: hideInstall}] = useBoolean();
    useEffect(
        () => {
            if (jetbrainsList?.length === 0) {
                displayInstall();
            } else {
                hideInstall();
            }
        },
        [displayInstall, hideInstall, jetbrainsList?.length, open]
    );
    return (
        <div onClick={e => e.stopPropagation()}>
            <Button type="text" icon={<img src={jetbrains} style={{height: 22}} />} onClick={show}>Jetbrains</Button>
            <Modal
                open={open}
                width={showInstall ? 1250 : 600}
                style={{maxHeight: '90vh', overflowY: 'auto'}}
                onCancel={hide}
                footer={null}
                title={
                    showInstall
                        ? null
                        : (
                            <StatusHeadWidthShare
                                isShared={!!jetbrainsList?.[0]?.shareUname}
                                type="jetbrains"
                                status={jetbrainsList?.[0]?.status}
                            />
                        )
                }
            >
                {
                    showInstall ? (
                        <InstallIdeGuide type="jetbrains" />
                    )
                        : (
                            <>
                                <p style={{marginBottom: 16}}>
                                    iCoding-Jetbrains 系列 IDE 进入维护状态，推荐您使用 AI IDE
                                </p>
                                {
                                    jetbrainsList.map(jetbrains => (
                                        <IdeWorkSpaceItem type="jetbrains" key={jetbrains.id} machine={jetbrains} />
                                    ))
                                }
                                <Flex justify="flex-end" style={{marginTop: 24}}>
                                    <Button type="primary" onClick={displayInstall}>安装更多 IDE</Button>
                                </Flex>
                            </>
                        )
                }
            </Modal>
        </div>
    );
};

export default OpenWidthJetbrains;

