import {useMemo, useCallback} from 'react';
import {Typo<PERSON>} from 'antd';
import {useBoolean} from 'huse';
import {Workspace} from '@/api/icode/webIDE';
import {handleStopPropagation} from '@/constants/icode/handlers';
import {AnyEvent} from '@/types/icode/common';
import {interactive} from '@/styles/icode/componentStyle';
import {getWorkspacesSorted} from '../utils';
import openWorkspace from '../openWorkspace';
import {enterStackMode} from '../useModalStackInfo';
import WorkspacesManageModal from '../WorkspacesManageModal';
import {CheckAll, WorkspaceLabelRow, WorkspaceLabel} from './styled';

interface Props {
    workspaces: Workspace[];
    supportVscode: boolean;
    supportJetbrains: boolean;
}

export const RecentWorkspaces = ({workspaces, supportVscode, supportJetbrains}: Props) => {
    const [
        workspacesManageModalVisible,
        {on: showWorkspacesManageModal, off: hideWorkspacesManageModal},
    ] = useBoolean(false);

    const sortedWorkspaces = useMemo(
        () => getWorkspacesSorted(workspaces),
        [workspaces]
    );

    const handleWorkspaceClick = useCallback(
        (e: AnyEvent, workspace: Workspace) => {
            handleStopPropagation(e);
            openWorkspace(workspace);
        },
        []
    );

    const handleWorkspacesManage = useCallback(
        (e: AnyEvent) => {
            handleStopPropagation(e);
            enterStackMode(showWorkspacesManageModal, hideWorkspacesManageModal);
            showWorkspacesManageModal();
        },
        [hideWorkspacesManageModal, showWorkspacesManageModal]
    );

    const checkAll = useMemo(
        () => <CheckAll className={interactive} onClick={handleWorkspacesManage}>查看全部</CheckAll>,
        [handleWorkspacesManage]
    );


    if (sortedWorkspaces.length === 0) {
        return <span>暂无</span>;
    }

    const firstRow = sortedWorkspaces.slice(0, 3);
    const secondRow = sortedWorkspaces.slice(3, 5);

    return (
        <>
            <WorkspaceLabelRow>
                {firstRow.map(workspace => (
                    <WorkspaceLabel
                        key={workspace.path}
                        onClick={handleWorkspacesManage}
                    >
                        <Typography.Text
                            ellipsis={{tooltip: true}}
                        >
                            {workspace?.editionName}
                        </Typography.Text>
                    </WorkspaceLabel>
                ))}
            </WorkspaceLabelRow>
            {secondRow.length > 0 && (
                <WorkspaceLabelRow>
                    {secondRow.map(workspace => (
                        <WorkspaceLabel
                            key={workspace.path}
                            onClick={e => handleWorkspaceClick(e, workspace)}
                        >
                            <Typography.Text
                                ellipsis={{tooltip: true}}
                                className={interactive}
                            >
                                {workspace?.editionName}
                            </Typography.Text>
                        </WorkspaceLabel>
                    ))}
                    {checkAll}
                </WorkspaceLabelRow>
            )}
            <div onClick={handleStopPropagation}>
                <WorkspacesManageModal
                    supportVscode={supportVscode}
                    supportJetbrains={supportJetbrains}
                    workspaces={sortedWorkspaces}
                    visible={workspacesManageModalVisible}
                    onCancel={hideWorkspacesManageModal}
                />
            </div>
        </>
    );
};
