import {ellipsis} from '@panda-design/components';
import {css} from '@emotion/css';
import styled from '@emotion/styled';
import {Divider, Flex, Space, Typography} from 'antd';
import {Workspace, Machine, MachineNew, ShardWorkSpaceByMachine} from '@/api/icode/webIDE';
import {BasicCard} from '@/design/icode/Card';
import {CopyButton} from '@/design/icode/Helpful/CopyButton';
import {IconCopy} from '@/icons-icode/actions';
import {RecentWorkspaces} from './RecentWorkspaces';
import OpenWidthAiIDE from './OpenWidthAiIDE';
import OpenWidthVscode from './OpenWIdthVscode';
import OpenWidthJetbrains from './OpenWidthJetbrains';

const Header = styled.div`
    margin-top: 8px;
    font-size: 12px;
    display: flex;
    height: 18px;
    justify-content: space-between;
`;

const contentCss = css`
    flex: 1;
    padding: 12px 0;
`;

interface MachineCardProps {
    machine: Omit<MachineNew | ShardWorkSpaceByMachine, 'jetbrains' | 'vscode'>;
    jetbrainsList: Array<Machine & {workspaces: Workspace[]}>;
    vscodeList: Array<Machine & {workspaces: Workspace[]}>;
}

// 开发机卡片
const MachineCard = (props: MachineCardProps) => {
    const {
        machine,
        jetbrainsList,
        vscodeList,
    } = props;
    const {ip} = machine ?? {};
    const supportVscode = vscodeList?.length > 0;
    const supportJetbrains = jetbrainsList?.length > 0;
    const name = (machine as MachineNew).name || (machine as ShardWorkSpaceByMachine).hostname;

    return (
        <BasicCard>
            <Typography.Text strong style={{maxWidth: '60%'}} ellipsis={{tooltip: true}}>
                {name}
            </Typography.Text>
            <Header>
                <Space>
                    <span style={{color: '#333333'}} className={ellipsis}>
                        {ip}
                    </span>
                    <CopyButton
                        style={{color: '#666666'}}
                        type="text"
                        size="small"
                        icon={<IconCopy />}
                        text={ip}
                        tooltip=""
                    />
                </Space>
                <span style={{color: '#999999'}}>
                    {(machine as MachineNew).createTime}
                </span>
            </Header>
            <div className={contentCss}>
                <RecentWorkspaces
                    supportVscode={supportVscode}
                    supportJetbrains={supportJetbrains}
                    workspaces={[...jetbrainsList, ...vscodeList].reduce(
                        (acc, {workspaces}) => [...acc, ...workspaces],
                        [] as Workspace[]
                    )}
                />
            </div>
            <Divider style={{margin: '12px 0'}} />

            <Flex justify="space-between">
                <OpenWidthAiIDE hostname={name} />
                <OpenWidthVscode vscodeList={vscodeList} />
                <OpenWidthJetbrains jetbrainsList={jetbrainsList} />
            </Flex>
        </BasicCard>
    );
};

export default MachineCard;
