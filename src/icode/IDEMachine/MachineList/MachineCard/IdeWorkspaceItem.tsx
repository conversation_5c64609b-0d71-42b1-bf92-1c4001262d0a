import styled from '@emotion/styled';
import {Space} from 'antd';
import {useCallback, useMemo} from 'react';
import {noop} from 'lodash';
import {Machine, Workspace} from '@/api/icode/webIDE';
import OperationButton, {OperationType} from '@/icode/WebIDE/OperationButton';
import InvokeClientOperation from '@/icode/WebIDE/InvokeClientOperation';
import ShareButton from '@/icode/WebIDE/ResourceShareModal/ShareButton';
import {showResourceShareModal} from '@/icode/WebIDE/ResourceShareModal/region';
import {getClientTypeFromIdeType, getIsSharedByOthers} from '@/icode/WebIDE/utils';
import {IconDelete1, IconRefresh, IconShare} from '@/icons-icode/webIDE';
import {IDEProductLabel} from '@/components/icode/IDEProductLabel';
import RestartButton from '../MachineOperations/RestartButton';
import DeclineSharedWorkspace from '../DeclineSharedWorkspace';
import {convertToIdentifiableIdeType, getLastUsedWorkspace} from '../utils';
import {setMachineDeleteInfo, showMachineDeleteModal} from '../MachineDeleteModal/region';
import {getWorkspaceJumpUrl} from '../openWorkspace';
import {getPath} from '../MachineOperations';

const Wrapper = styled.div`
    padding: 8px 16px;
    background-color: #F5F5F5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
`;

const ProductWrapper = styled.span`
    display: inline-block;
    min-width: 150px;
`;

interface Props {
    type: 'vscode' | 'jetbrains';
    machine: Machine & {workspaces: Workspace[]};
}

const IdeWorkSpaceItem = ({type, machine}: Props) => {
    const {mtime, workspaces} = machine;
    const workspace = useMemo(
        () => getLastUsedWorkspace(workspaces),
        [workspaces]
    );
    const handleMachineDelete = useCallback(
        () => {
            setMachineDeleteInfo({machine, workspaces});
            showMachineDeleteModal();
        },
        [machine, workspaces]
    );

    const handleShareClick = useCallback(
        () => {
            if (!workspace) {
                return;
            }
            showResourceShareModal({
                kind: 'workspace',
                workspaceId: workspace.id,
                name: workspace.editionPath || workspace.name,
                hostname: workspace.hostname,
                editionName: workspace.editionName || '',
                jumpUrl: getWorkspaceJumpUrl(workspace),
            });
        },
        [workspace]
    );

    const isSharedByOthers = workspace !== undefined && getIsSharedByOthers(workspace);
    const identifiableIdeType = convertToIdentifiableIdeType(machine.ideType);

    const hasNoWorkspaces = workspaces.length === 0;

    const privilegedOperartionsDisabled = isSharedByOthers || hasNoWorkspaces;

    const client = getClientTypeFromIdeType(workspace.ideType);

    const isShareable = !isSharedByOthers;
    return (
        <Wrapper>
            {type === 'jetbrains' && (
                <ProductWrapper>
                    <IDEProductLabel ideType={identifiableIdeType} />
                </ProductWrapper>
            )}
            <span>{mtime}</span>
            <Space>
                <InvokeClientOperation
                    mid={machine.id}
                    path={getPath(workspace)}
                    ideaWsUrl={workspace.panDomain}
                    isAgentOffline={workspace.isAgentOffline}
                    cmd={workspace.cmd}
                    status={workspace.status}
                    id={workspace.id}
                    client={client}
                    resourceType="devServer"
                    buttonStyle="borderless"
                />
                <RestartButton
                    icon={<IconRefresh />}
                    buttonStyle="borderless"
                    disabled={privilegedOperartionsDisabled}
                    workspace={workspace}
                />
                {isShareable && (
                    <ShareButton
                        kind="workspace"
                        icon={<IconShare />}
                        onClick={handleShareClick}
                        buttonStyle="borderless"
                    />)
                }
                {isSharedByOthers && <DeclineSharedWorkspace workspaceId={workspace.id} buttonStyle="borderless" />}
                <OperationButton
                    type={OperationType.Delete}
                    disabled={privilegedOperartionsDisabled}
                    tooltipMessage="卸载iCoding"
                    icon={<IconDelete1 />}
                    onClick={privilegedOperartionsDisabled ? noop : handleMachineDelete}
                />
            </Space>
        </Wrapper>
    );
};

export default IdeWorkSpaceItem;

