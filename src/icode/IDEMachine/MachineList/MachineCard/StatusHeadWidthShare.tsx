import styled from '@emotion/styled';
import {Flex, Space} from 'antd';
import {Tag} from '@panda-design/components';
import StatusIndicator from '@/icode/WebIDE/StatusIndicator';
import {IconVscode} from '@/icons-icode/product';
import jetbrains from '../assets/jetbrains.png';

const Title = styled.span`
    font-size: 20px;
    font-weightL 500;
`;

interface Props {
    type: 'vscode' | 'jetbrains';
    status: string;
    isShared?: boolean;
}

const StatusHeadWidthShare = ({type, isShared, status}: Props) => {
    return (
        <Flex justify="space-between" align="center">
            <Space size={12}>
                <Title>
                    {
                        type === 'vscode'
                            ? <IconVscode style={{width: 20, marginRight: 4}} />
                            : <img src={jetbrains} style={{width: 20, marginRight: 4}} />
                    }

                    { type === 'vscode' ? 'VS Code' : 'Jetbrains 系列'}
                </Title>
                <StatusIndicator status={status} resourceType="devServer" />
            </Space>
            {
                isShared && (
                    <Tag style={{marginRight: 32}} type="flat" color="info">分享</Tag>
                )
            }
        </Flex>
    );
};

export default StatusHeadWidthShare;

