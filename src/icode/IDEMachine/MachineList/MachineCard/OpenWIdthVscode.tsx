import {Button, Modal} from '@panda-design/components';
import {useBoolean} from 'huse';
import {ProductIcon} from '@/components/icode/IDEProductLabel/ProductIcon';
import InstallIdeGuide from '@/icode/WebIDE/MachineNew/InstallIdeGuide';
import {Machine, Workspace} from '@/api/icode/webIDE';
import StatusHeadWidthShare from './StatusHeadWidthShare';
import IdeWorkSpaceItem from './IdeWorkspaceItem';

interface Props {
    vscodeList: Array<Machine & {workspaces: Workspace[]}>;
}

const OpenWidthVscode = ({vscodeList}: Props) => {
    const [open, {on: show, off: hide}] = useBoolean();
    return (
        <div onClick={e => e.stopPropagation()}>
            <Button type="text" icon={<ProductIcon name="vscode" size={22} />} onClick={show}>VS Code</Button>
            <Modal
                open={open}
                width={vscodeList?.length > 0 ? 600 : 1250}
                style={{maxHeight: '90vh', overflowY: 'auto'}}
                onCancel={hide}
                footer={null}
                title={
                    vscodeList?.length > 0
                        ? (
                            <StatusHeadWidthShare
                                isShared={!!vscodeList?.[0]?.shareUname}
                                type="vscode"
                                status={vscodeList?.[0]?.status}
                            />
                        )
                        : null
                }
            >
                {
                    vscodeList?.length > 0 ? (
                        <>
                            <p style={{marginBottom: 16}}>
                                iCoding-VS Code 进入维护状态，推荐您使用 AI IDE
                            </p>
                            {vscodeList.map(vscode => (
                                <IdeWorkSpaceItem type="vscode" key={vscode.id} machine={vscode} />
                            ))}
                        </>
                    )
                        : (
                            <InstallIdeGuide type="vscode" />
                        )
                }
            </Modal>
        </div>
    );
};

export default OpenWidthVscode;

