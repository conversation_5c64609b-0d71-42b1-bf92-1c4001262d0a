import {Button} from '@panda-design/components';
import {useCallback} from 'react';
import {ProductIcon} from '@/components/icode/IDEProductLabel/ProductIcon';
import {invokeAiIDEClientWidthHostname} from '@/icode/WebIDE/InvokeClientOperation/invokeClients';
import {openDownloadModal} from '@/icode/WebIDE/DownloadAiIDEModal/region';

interface Props {
    hostname: string;
}

const OpenWidthAiIDE = ({hostname}: Props) => {
    const toIDE = useCallback(
        () => {
            invokeAiIDEClientWidthHostname(hostname).catch(openDownloadModal);
        },
        [hostname]
    );
    return (
        <div onClick={e => e.stopPropagation()}>
            <Button type="text" icon={<ProductIcon name="aiIDE" size={22} />} onClick={toIDE}>
                AI IDE
            </Button>
        </div>
    );
};

export default OpenWidthAiIDE;

