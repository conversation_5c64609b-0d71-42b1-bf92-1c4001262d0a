import {Button, message} from '@panda-design/components';
import {useBoolean} from 'huse';
import {useCallback, useState, useEffect} from 'react';
// eslint-disable-next-line max-len
import {useLocation} from 'react-router-dom';
import {useSearchParamsUpdate} from '@panda-design/router';
import {
    apiPostBatchSearchSubmit,
    ParamsPostBatchSearchSubmit,
    apiGetPageBatchSearchResult,
    PageBatchSearchResult,
    apiGetBatchSearchCondition,
} from '@/api/icode/batchSearch';
import {usePageAndPageSize} from '@/hooks/icode/paging';
import BatchSearchModal from './BatchSearchModal';
import BatchSearchLoadingModal from './BatchSearchModal/BatchSearchLoadingModal';
import {BatchSearchPreviewDrawer} from './BatchSearchPreviewDrawer';

const BatchSearch = () => {
    const [open, {on: showOpen, off: hideOpen}] = useBoolean(false);
    const [drawerOpen, {on: showDrawerOpen, off: hideDrawerOpen}] = useBoolean(false);
    const [loadingModalOpen, {on: showLoadingModalOpen, off: hideLoadingModalOpen}] = useBoolean(false);
    const [submitParams, setSubmitParams] = useState<ParamsPostBatchSearchSubmit>(
        {
            dataset: '', username: '', files: {}, filter: {
                conjunction: '',
                items: [],
            },
        });
    const [data, setData] = useState<PageBatchSearchResult|null>(null);
    const [filterData, setFilterData] = useState<ParamsPostBatchSearchSubmit|null>();
    const location = useLocation();
    const {pathname, search} = location;
    const batchResult = pathname.includes('batchResult');
    const searchParams = new URLSearchParams(search);
    const paramsQueryId = searchParams.get('queryId');
    const replace = useSearchParamsUpdate();
    const {page, pageSize, onChange} = usePageAndPageSize();


    const handlePageChange = (newPage: number, newPageSize: number) => {
        replace({page: newPage, pageSize: newPageSize});
        onChange(newPage, newPageSize);
    };


    const handleOnOk = useCallback(
        async () => {
            try {
                hideOpen(); // 关闭当前模态框
                await apiPostBatchSearchSubmit(submitParams);
                showLoadingModalOpen(); // 打开新模态框
            } catch (error) {
                message.error('批量检索失败');
            }
        },
        [hideOpen, showLoadingModalOpen, submitParams]
    );

    const fetchData = useCallback(
        async () => {
            const pageData = await apiGetPageBatchSearchResult({
                queryId: paramsQueryId,
                pageNum: 1,
                pageSize: 10,
            });
            const filterData = await apiGetBatchSearchCondition({
                queryId: paramsQueryId,
            });

            setData(pageData);
            setFilterData(filterData);
            showDrawerOpen();
        },
        [paramsQueryId, showDrawerOpen]
    );


    useEffect(
        () => {
        // 当组件挂载时，检查路由参数决定是否打开抽屉
            if (batchResult && paramsQueryId) {
                fetchData();
            }
        },
        [batchResult, fetchData, paramsQueryId, showDrawerOpen]
    );

    return (
        <>
            <Button
                type="text"
                size="middle"
                onClick={showOpen}
            >
                批量检索
            </Button>
            <BatchSearchModal
                open={open}
                onClose={hideOpen}
                handleOnOk={handleOnOk}
                setSubmitParams={setSubmitParams}
            />
            <BatchSearchLoadingModal
                open={loadingModalOpen}
                handleOnOk={hideLoadingModalOpen}
                onClose={hideLoadingModalOpen}
            />
            <BatchSearchPreviewDrawer
                page={page}
                pageSize={pageSize}
                onChange={handlePageChange}
                open={drawerOpen}
                onClose={hideDrawerOpen}
                data={data}
                setData={setData}
                queryId={paramsQueryId}
                filterData={filterData}
            />
        </>
    );
};

export default BatchSearch;
