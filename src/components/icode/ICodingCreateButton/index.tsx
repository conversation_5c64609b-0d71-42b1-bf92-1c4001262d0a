/**
 * @file Web IDE 开发资源创建逻辑组件，用于包裹各种按钮，方便逻辑统一索引
 */
import {useCallback, ReactNode, CSSProperties} from 'react';
import {useShortKey} from 'use-short-key';
import {noop, partial} from 'lodash';
import {message} from '@panda-design/components';
import {useBoolean} from 'huse';
import {useCreateResourceCallback} from '@/hooks/icode/webIDE/useCreateResourceCallback';
import {CreateFromCodeParams} from '@/api/icode/webIDE';
import {track} from '@/utils/icode/track';
import {getCodeSpaceTemplateIdByRepoInfo} from '@/utils/icode/webIDE';
import {REAL_TEMPLATE_ID_MAP} from '@/constants/icode/webIDE';
import useRepoFrozenStatus from '@/hooks/icode/repoSettings/useRepoFrozenStatus';
import {FrozenStatus} from '@/types/icode/repo';
import {useShortKeysEnable} from '@/regions/icode/localStorage';
import {DownloadAiIDEModal} from '@/icode/WebIDE/DownloadAiIDEModal';
import {invokeAiIDEClient} from '@/icode/WebIDE/InvokeClientOperation/invokeClients';
import IDESelectPopover from './IDESelectPopover';
import {QuickCodeSpaceCreateOptions} from './IDESelectPopover/CodeSpaceQuickCreateForm';

type CreateFromCodeParamsBase = Omit<CreateFromCodeParams, 'standbyTime' | 'language' | 'templateId'>;

interface CreateResourceOperationProps extends CreateFromCodeParamsBase {
    eventLabel: string;
    enableKeyPress?: boolean;
    className?: string;
    children?: ReactNode;
    /** 代码库模版配置 */
    templateName: string | null;
    /** 代码库配置的语言 */
    languageReal: string | null;
    enableIdeTypeSelect?: boolean;
    wrapperStyle?: CSSProperties;
    frozenStatus?: FrozenStatus;
    beforeCreate?: () => void;
}

// 之所以没有提供一个纯 hook，而是封装成了组件，是因为要塞 contextHolder，且不想用 hoc
// 对于 Table Column render function 的场景，提供 hook 也不方便直接使用
const ICodingCreateButton = (props: CreateResourceOperationProps) => {
    const {
        eventLabel,
        enableKeyPress,
        className,
        children,
        templateName,
        languageReal,
        enableIdeTypeSelect = true,
        wrapperStyle,
        frozenStatus,
        beforeCreate,
        pullType,
        pullValue,
        codeRepo,
        initPath,
        reviewStatus,
        resourceName,
    } = props;

    const repoFrozenStatus = useRepoFrozenStatus() || frozenStatus;
    const [downloadModalOpen, {on: show, off: hide}] = useBoolean();

    const [createResource, contextHolder] = useCreateResourceCallback({
        pullType,
        pullValue,
        codeRepo,
        language: languageReal ?? '', // language 字段改为传代码库的实际语言，为空时传空字符串
        initPath,
        reviewStatus,
        resourceName,
        templateId: REAL_TEMPLATE_ID_MAP.aiIDE, // 提供默认值
        openAiIdeCallback: resourceId => invokeAiIDEClient(resourceId).catch(show),
    });

    const enable = useShortKeysEnable();

    const handleCodeSpaceCreate = useCallback(
        ({templateId, ideType, dockerEnabled}: QuickCodeSpaceCreateOptions) => {
            beforeCreate && beforeCreate();
            message.loading('代码空间创建中，请稍候，即将跳转...');
            track('创建IDE', eventLabel);
            // pullType 为 diff 时对应评审空间，因此用默认的 vscode 镜像打开
            if (pullType === 'diff') {
                createResource();
                return;
            }
            // 根据代码库的配置和用户选择的 IDE 类型，得到给代码空间设定的语言和模板
            const finalTemplateId = templateId ?? getCodeSpaceTemplateIdByRepoInfo({
                languageReal,
                templateName,
                ideType,
            });
            createResource({templateId: finalTemplateId, useDocker: dockerEnabled});
        },
        [beforeCreate, createResource, eventLabel, languageReal, pullType, templateName]
    );

    const handleDefaultCodeSpaceCreate = partial(handleCodeSpaceCreate, {ideType: 'vscode'});

    useShortKey({
        code: 'Period',
        keydown: enableKeyPress && repoFrozenStatus !== FrozenStatus.FROZEN && enable
            ? handleDefaultCodeSpaceCreate : undefined,
    });

    const createButton = (
        <div style={{width: 'fit-content', ...wrapperStyle}}>
            <span
                className={className}
                onClick={enableIdeTypeSelect ? noop : handleDefaultCodeSpaceCreate}
            >
                {children}
            </span>
            <span>{contextHolder}</span>
        </div>
    );

    if (enableIdeTypeSelect) {
        return (
            <>
                <DownloadAiIDEModal onCancel={hide} open={downloadModalOpen} />
                <IDESelectPopover
                    onConfirm={handleCodeSpaceCreate}
                    frozenStatus={repoFrozenStatus}
                    language={languageReal}
                    codeRepo={codeRepo}
                    pullType={pullType}
                    pullValue={pullValue}
                    templateName={templateName}
                    trigger="click"
                >
                    {createButton}
                </IDESelectPopover>
            </>
        );
    }

    return createButton;
};

export default ICodingCreateButton;
