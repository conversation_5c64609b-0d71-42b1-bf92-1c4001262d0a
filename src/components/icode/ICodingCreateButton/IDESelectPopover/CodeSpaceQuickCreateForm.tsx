import {Form, Radio, Row, Select, Space, Typography} from 'antd';
import {<PERSON>ton, HelpIcon} from '@panda-design/components';
import {css, cx} from '@emotion/css';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {useBoolean, useRequest} from 'huse';
import styled from '@emotion/styled';
import {IconAdd} from '@baidu/ee-icon';
import {IdeType} from '@/types/icode/webIDE';
import {IDE_DISPLAY_NAME_BY_TYPE} from '@/constants/icode/webIDE';
import {Label} from '@/design/icode/FormElements';
import {apiGetResourcesByRepoInfos, apiGetTemplateByCodeRepo, PullType} from '@/api/icode/webIDE';
import {IconArrow} from '@/icons-icode/webIDE';
import {IDEProductLabel} from '@/components/icode/IDEProductLabel';
import {token} from '@/utils/icode/theme';
import {myToken} from '@/constants/colors';
import {getCodeSpaceTemplateIdByRepoInfo} from '@/utils/icode/webIDE';
import {FormItem, IDEBox, StyledIconCheckSolid} from './styled';
import {getIDEOptions} from './getIDEOptions';

const HorizontalContainer = styled.div`
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;
`;

const StyledHelpIcon = styled(HelpIcon)`
    margin-left: auto;
`;

const interactiveBordered = css`
    cursor: pointer;
    border: ${myToken.border};

    &:hover {
        color: ${token.colorPrimaryHover};
        border-color: ${token.colorPrimaryHover};
    }

    &:active {
        color: ${token.colorPrimaryActive};
        border-color: ${token.colorPrimaryActive};
    }
`;

const formStyle = {
    labelCol: {
        flex: '72px',
    },
    style: {
        margin: '8px 0',
    },
};

export interface QuickCodeSpaceCreateOptions {
    ideType: IdeType;
    dockerEnabled?: boolean;
    templateId?: number;
}

interface Props {
    language: string | null;
    onConfirm: (params: QuickCodeSpaceCreateOptions) => void;
    onCancel: () => void;
    codeRepo: string;
    // 代码库创建时所选模版
    templateName: string | null;
    pullType: PullType;
    pullValue: string;
}

const CodeSpaceQuickCreateForm = (props: Props) => {
    const {onConfirm, onCancel, codeRepo, pullType, pullValue, language, templateName} = props;
    const {pending: templatesLoading, data: templates} = useRequest(
        apiGetTemplateByCodeRepo,
        {codeRepo}
    );
    const {pending: existingCodeSpacesLoading, data: existingCodeSpaces} = useRequest(
        apiGetResourcesByRepoInfos,
        {codeRepo, pullValue, pullType}
    );
    const [dockerEnabled, {toggle: toggleDockerEnabled}] = useBoolean(false);
    const [ideType, setIdeType] = useState<IdeType>('aiIDE');
    const [templateId, setTemplateId] = useState<number>();
    const isLoading = templatesLoading || existingCodeSpacesLoading;

    const handleConfirm = useCallback(
        () => {
            onConfirm({ideType, dockerEnabled, templateId});
            onCancel();
        },
        [onConfirm, ideType, dockerEnabled, templateId, onCancel]
    );
    const ideOptions = useMemo(
        () => getIDEOptions(language),
        [language]
    );

    useEffect(
        () => {
            if (templates === undefined) {
                return;
            }
            if (templates.length === 0) {
                return;
            }
            if (ideType !== 'aiIDE') {
                setTemplateId(getCodeSpaceTemplateIdByRepoInfo({
                    ideType,
                    languageReal: language,
                    templateName,
                }));
            }
            else {
                setTemplateId(21);
            }
        },
        [templates, ideType, language, templateName]
    );

    const matchedCodeSpace = useMemo(
        () => {
            return existingCodeSpaces?.find(item => (
                item.pullType === pullType
                && item.pullValue === pullValue
                && parseInt(item.templateId, 10) === templateId
            ));
        },
        [existingCodeSpaces, pullType, pullValue, templateId]
    );

    return (
        <div id="codeSpaceQuickCreateFormContainer" style={{position: 'relative'}}>
            <Form
                colon={false}
                labelAlign="left"
                labelCol={formStyle.labelCol}
                style={formStyle.style}
            >
                <FormItem label={<Label>IDE 类型</Label>}>
                    <HorizontalContainer>
                        {ideOptions.map(({type}) => (
                            <IDEBox
                                key={type}
                                className={cx(interactiveBordered, {'checked': ideType === type})}
                                onClick={() => setIdeType(type)}
                            >
                                {ideType === type && <StyledIconCheckSolid />}
                                <IDEProductLabel ideType={type} showLabel={false} size={24} />
                                {IDE_DISPLAY_NAME_BY_TYPE[type]}
                            </IDEBox>
                        ))}
                    </HorizontalContainer>
                </FormItem>
                {ideType === 'vscode' && (
                    <FormItem label={<Label>模板</Label>}>
                        <Select
                            placeholder="请选择模版"
                            value={templateId}
                            onChange={setTemplateId}
                            getPopupContainer={
                                () => document.getElementById('codeSpaceQuickCreateFormContainer')
                            }
                        >
                            {(templates ?? []).map(item => (
                                <Select.Option key={item.id} value={item.id}>
                                    <HorizontalContainer>
                                        <Typography.Text ellipsis={{tooltip: true}}>
                                            {item.templateName}
                                        </Typography.Text>
                                        <StyledHelpIcon tooltip={item.describeInfo || '暂无说明'} />
                                    </HorizontalContainer>
                                </Select.Option>
                            ))}
                        </Select>
                    </FormItem>
                )}
                <FormItem
                    label={<Label>Docker</Label>}
                    tooltip={{title: '在 WebIDE 内安装并启动 Docker', placement: 'left'}}
                >
                    <Radio.Group
                        onChange={toggleDockerEnabled}
                        value={matchedCodeSpace ? matchedCodeSpace.useDocker : dockerEnabled}
                        defaultValue={dockerEnabled}
                        options={[{label: '启用', value: true}, {label: '不启用', value: false}]}
                        disabled={matchedCodeSpace !== undefined}
                    />
                </FormItem>
                <Row justify="end">
                    <Space size={12}>
                        {matchedCodeSpace && (
                            <Typography.Text type="success">已创建</Typography.Text>
                        )}
                        <Button size="small" onClick={onCancel}>取消</Button>
                        <Button
                            size="small"
                            type="primary"
                            onClick={handleConfirm}
                            disabled={isLoading}
                            icon={matchedCodeSpace ? <IconArrow /> : <IconAdd />}
                        >
                            {matchedCodeSpace ? '打开' : '创建'}
                        </Button>
                    </Space>
                </Row>
            </Form>
        </div>
    );
};

export default CodeSpaceQuickCreateForm;
