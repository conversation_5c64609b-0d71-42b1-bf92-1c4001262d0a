import {IdeType} from '@/types/icode/webIDE';

interface IDEOption {
    type: IdeType;
}

const aiIDE: IDEOption = {
    type: 'aiIDE',
};

const idea: IDEOption = {
    type: 'ideaCommunity',
};

const pycharm: IDEOption = {
    type: 'pycharm',
};

const goland: IDEOption = {
    type: 'goland',
};

export const getIDEOptions = (language: string | null): IDEOption[] => [
    aiIDE,
    ...(language === 'java' ? [idea] : []),
    ...(language === 'python' ? [pycharm] : []),
    ...(language === 'go' ? [goland] : []),
];
