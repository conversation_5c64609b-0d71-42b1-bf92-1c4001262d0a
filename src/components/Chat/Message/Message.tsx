import {useMemo} from 'react';
import {Flex} from 'antd';
import styled from '@emotion/styled';
import {useCurrentChat, useMessage} from '@/regions/staff/chat';
import {staffColors} from '@/constants/colors/staff';
import {Elements} from '../Element';
import {MessageProvider} from '../Provider/MessageProvider';
import StaffAvatar from '../StaffAvatar';
import {useStaffTypeIsChat} from '../Provider/StaffTypeProvider';

const NameText = styled.div`
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 12px;
    letter-spacing: 0px;
    color: #727272;
    text-align: center;
`;

const UserContainer = styled.div`
    display: flex;
    flex-direction: column;
    gap: 4px;
    justify-content: flex-end;
    padding: 5px 16px;
    margin-left: 32px;
    background: ${staffColors.gradientBg};
    border-top-left-radius: 20px;
    border-bottom-right-radius: 12px;
    border-bottom-left-radius: 20px;
`;

interface Props {
    messageId: string;
}

export const Message = ({messageId}: Props) => {
    const message = useMessage(messageId);
    const {agentName, agentId} = useCurrentChat();
    const staffTypeIsChat = useStaffTypeIsChat();

    const isBot = useMemo(
        () => {
            return message?.role === 'AGENT';
        },
        [message?.role]
    );

    return (
        <MessageProvider messageId={messageId} agentId={agentId}>
            <Flex vertical gap={12} style={{width: '100%'}}>
                {isBot && staffTypeIsChat && (
                    <Flex gap={8} align="center">
                        <StaffAvatar
                            agentId={agentId}
                            username={agentName}
                            iconSize={24}
                        />
                        <NameText>{agentName}</NameText>
                    </Flex>
                )}
                {isBot ? (
                    <Flex vertical gap={4}>
                        <Elements items={message?.elements} />
                    </Flex>
                ) : (
                    <Flex justify="end">
                        <UserContainer>
                            <Elements items={message?.elements} />
                        </UserContainer>
                    </Flex>
                )}
            </Flex>
        </MessageProvider>
    );
};
