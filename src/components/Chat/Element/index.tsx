/* eslint-disable complexity */
import {ToolElement} from '@/types/staff/element';
import {ToolActions} from './ToolActions';
import {ToolSteps} from './ToolSteps';
import {ToolFileContext} from './ToolFileContext';
import {ToolText} from './ToolText';
import {ToolField} from './ToolField';
import {ToolSmartText} from './ToolSmartText';
import {ToolCard} from './ToolCard';
import {ToolImage} from './ToolImage';
import {ToolIframe} from './ToolIframe';
import {ToolStageSteps} from './ToolStageSteps';
import {ToolForm} from './ToolForm';
import {ToolInfoflowCardWebUrl} from './ToolCardIFrame';
import ToolMCPCall from './ToolMCPCall';
interface Props {
    item: ToolElement;
}

export const Element = ({item}: Props) => {
    switch (item?.type) {
        case 'text':
            return <ToolText item={item} />;
        case 'smartText':
            return <ToolSmartText item={item} />;
        case 'actions':
            return <ToolActions item={item} />;
        case 'fileContext':
            return <ToolFileContext item={item} />;
        case 'steps':
            return <ToolSteps item={item} />;
        case 'stageSteps':
            return <ToolStageSteps item={item} />;
        case 'field':
            return <ToolField item={item} />;
        case 'smartLink':
            return <ToolSmartText item={item} />;
        case 'card':
            return <ToolCard item={item} />;
        case 'image':
            return <ToolImage item={item} />;
        case 'iframe':
            return <ToolIframe item={item} />;
        case 'form':
            return <ToolForm item={item} />;
        case 'infoflowCardWebUrl':
            return <ToolInfoflowCardWebUrl item={item} />;
        case 'toolCall':
            return <ToolMCPCall item={item} />;
    }
};

interface ElementsProps {
    items: ToolElement[];
}

export const Elements = ({items}: ElementsProps) => {
    return (
        <>
            {(items ?? []).map((item, index) => {
                return <Element key={index} item={item} />;
            })}
        </>
    );
};
