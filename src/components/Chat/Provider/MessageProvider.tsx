import constate from 'constate';
import {useEffect, useState} from 'react';

interface InitType {
    messageId: string;
    agentId: number;
    handleCloseNotification?: () => void;
}

const useMessageContextRaw = (initialValue: InitType) => {
    const [messageId, setMessageId] = useState<string>(initialValue.messageId);
    const [agentId, setAgentId] = useState<number>(initialValue.agentId);
    const isNotification = !!initialValue?.handleCloseNotification;
    const handleCloseNotification =
        initialValue?.handleCloseNotification || (() => {});

    useEffect(
        () => {
            setMessageId(initialValue.messageId);
            setAgentId(initialValue.agentId);
        },
        [initialValue]
    );

    return {messageId, setMessageId, agentId, isNotification, handleCloseNotification};
};

export const [
    MessageProvider,
    useMessageContext,
] = constate(
    useMessageContextRaw
);
