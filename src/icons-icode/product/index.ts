import type { FC, SVGProps } from "react";
import { createIcon } from "@/utils/icode/createIcon";
import SvgLinux from '@/assets/icode/webide/Linux';
import SvgDocker from '@/assets/icode/webide/Docker';
import AiIde from "./AiIde";
import AndroidStudio from "./AndroidStudio";
import Clion from "./Clion";
import Goland from "./Goland";
import IdeaCommunity from "./IdeaCommunity";
import IdeaUltimate from "./IdeaUltimate";
import Phpstorm from "./Phpstorm";
import Pycharm from "./Pycharm";
import Rust from "./Rust";
import Swan from "./Swan";
import Vscode from "./Vscode";

export const IconAiIde = createIcon(AiIde);
export const IconAndroidStudio = createIcon(AndroidStudio);
export const IconClion = createIcon(Clion);
export const IconGoland = createIcon(Goland);
export const IconIdeaCommunity = createIcon(IdeaCommunity);
export const IconIdeaUltimate = createIcon(IdeaUltimate);
export const IconPhpstorm = createIcon(Phpstorm);
export const IconPycharm = createIcon(Pycharm);
export const IconRust = createIcon(Rust);
export const IconSwan = createIcon(Swan);
export const IconVscode = createIcon(Vscode);

export const iconsMap: Record<string, FC<SVGProps<SVGSVGElement>>> = {
    aiIDE: IconAiIde,
    androidStudio: IconAndroidStudio,
    clion: IconClion,
    goland: IconGoland,
    ideaCommunity: IconIdeaCommunity,
    ideaUltimate: IconIdeaUltimate,
    phpstorm: IconPhpstorm,
    pycharm: IconPycharm,
    rust: IconRust,
    swan: IconSwan,
    vscode: IconVscode,
    linux: SvgLinux,
    docker: SvgDocker,
};

export default iconsMap;
