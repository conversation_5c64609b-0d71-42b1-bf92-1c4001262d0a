import type { FC, SVGProps } from "react";
import { createIcon } from "@/utils/icode/createIcon";
import <PERSON><PERSON> from "./Aio";
import <PERSON> from "./Arrow";
import Attention from "./Attention";
import Cpp from "./Cpp";
import <PERSON><PERSON> from "./Cpu";
import Delete from "./Delete";
import Document from "./Document";
import Error from "./Error";
import Go from "./Go";
import Gpu from "./Gpu";
import HardDisk from "./HardDisk";
import Intranet from "./Intranet";
import Java from "./Java";
import Js from "./Js";
import Link from "./Link";
import Loading from "./Loading";
import Memory from "./Memory";
import More from "./More";
import OpenInBrowser from "./OpenInBrowser";
import OpenInWeb from "./OpenInWeb";
import OpenOnDesktop from "./OpenOnDesktop";
import Package from "./Package";
import Pause from "./Pause";
import Php from "./Php";
import Play from "./Play";
import Python from "./Python";
import ReinstallOs from "./ReinstallOs";
import Restart from "./Restart";
import Ssh from "./Ssh";
import StopShare from "./StopShare";
import Success from "./Success";
import Terminal from "./Terminal";
import Tips from "./Tips";
import VscodeGray from "./VscodeGray";
import Waiting from "./Waiting";
import Windows from "./Windows";
import Apple from "./Apple";
import Delete1 from "./Delete1";
import Refresh from "./Refresh";
import Share from "./Share";


export const IconAio = createIcon(Aio);
export const IconArrow = createIcon(Arrow);
export const IconAttention = createIcon(Attention);
export const IconCpp = createIcon(Cpp);
export const IconCpu = createIcon(Cpu);
export const IconDelete = createIcon(Delete);
export const IconDocument = createIcon(Document);
export const IconError = createIcon(Error);
export const IconGo = createIcon(Go);
export const IconGpu = createIcon(Gpu);
export const IconHardDisk = createIcon(HardDisk);
export const IconIntranet = createIcon(Intranet);
export const IconJava = createIcon(Java);
export const IconJs = createIcon(Js);
export const IconLink = createIcon(Link);
export const IconLoading = createIcon(Loading);
export const IconMemory = createIcon(Memory);
export const IconMore = createIcon(More);
export const IconOpenInBrowser = createIcon(OpenInBrowser);
export const IconOpenInWeb = createIcon(OpenInWeb);
export const IconOpenOnDesktop = createIcon(OpenOnDesktop);
export const IconPackage = createIcon(Package);
export const IconPause = createIcon(Pause);
export const IconPhp = createIcon(Php);
export const IconPlay = createIcon(Play);
export const IconPython = createIcon(Python);
export const IconReinstallOs = createIcon(ReinstallOs);
export const IconRestart = createIcon(Restart);
export const IconSsh = createIcon(Ssh);
export const IconStopShare = createIcon(StopShare);
export const IconSuccess = createIcon(Success);
export const IconTerminal = createIcon(Terminal);
export const IconTips = createIcon(Tips);
export const IconVscodeGray = createIcon(VscodeGray);
export const IconWaiting = createIcon(Waiting);
export const IconWindows = createIcon(Windows);
export const IconApple = createIcon(Apple);
export const IconDelete1 = createIcon(Delete1);
export const IconRefresh = createIcon(Refresh);
export const IconShare = createIcon(Share);

export const iconsMap: Record<string, FC<SVGProps<SVGSVGElement>>> = {
    aio: IconAio,
    arrow: IconArrow,
    attention: IconAttention,
    cpp: IconCpp,
    cpu: IconCpu,
    delete: IconDelete,
    document: IconDocument,
    error: IconError,
    go: IconGo,
    gpu: IconGpu,
    hardDisk: IconHardDisk,
    intranet: IconIntranet,
    java: IconJava,
    js: IconJs,
    link: IconLink,
    loading: IconLoading,
    memory: IconMemory,
    more: IconMore,
    openInBrowser: IconOpenInBrowser,
    openInWeb: IconOpenInWeb,
    openOnDesktop: IconOpenOnDesktop,
    package: IconPackage,
    pause: IconPause,
    php: IconPhp,
    play: IconPlay,
    python: IconPython,
    reinstallOS: IconReinstallOs,
    restart: IconRestart,
    ssh: IconSsh,
    stopShare: IconStopShare,
    success: IconSuccess,
    terminal: IconTerminal,
    tips: IconTips,
    "vscode-gray": IconVscodeGray,
    waiting: IconWaiting,
    windows: IconWindows,
    apple: IconApple,
    delete1: IconDelete1,
    refresh: IconRefresh,
    share: IconShare,
};

export default iconsMap;
