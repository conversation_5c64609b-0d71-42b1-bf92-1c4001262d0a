import type {SVGProps} from "react";
const SvgDelete1 = (props: SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" {...props}>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.25 4.05469V5.30469H13.5V13.5312C13.5 14.1526 12.9963 14.6562 12.375 14.6562H3.625C3.00368 14.6562 2.5 14.1526 2.5 13.5312V5.30469H1.75V4.05469H14.25ZM12.25 5.30469H3.75V12.9688C3.75 13.208 3.94198 13.4023 4.18027 13.4062L4.1875 13.4062H11.8125C12.0517 13.4062 12.2461 13.2143 12.2499 12.976L12.25 12.9688V5.30469ZM9.95312 7.32031V11.0703H8.70312V7.32031H9.95312ZM7.29688 7.32031V11.0703H6.04688V7.32031H7.29688ZM10.6562 1.39844V2.64844H5.34375V1.39844H10.6562Z" fill="black" />
    </svg>
);
export default SvgDelete1;
