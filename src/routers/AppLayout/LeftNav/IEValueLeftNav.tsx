import {LeftNavigation, LeftNavigationMenuItem} from '@panda-design/router';
import {createRegion} from 'region-react';
import {IconLogo} from '@/icons/ievalueSidebar';
import {LabLink} from '@/links/ievalue/home';
import {useIEValueItems} from './useIEValueItems';

const collapsedRegion = createRegion(true, {
    withLocalStorageKey: 'iEValue/LeftNavigationCollapsed',
});
export const IEValueLeftNav = () => {
    const collapsed = collapsedRegion.useValue();
    const items: LeftNavigationMenuItem[] = useIEValueItems();

    return (
        <LeftNavigation
            collapsed={collapsed}
            style={{gap: 0}}
            onCollapse={collapsedRegion.set}
            logo={{
                icon: <IconLogo />,
                title: 'iEValue',
                to: LabLink.toUrl(),
            }}
            items={items}
            defaultCollapsed
        />
    );
};
