import {IconHome} from '@baidu/devops-components';
import {LeftNavigation, useSearchParams} from '@panda-design/router';
import {useMemo} from 'react';
import {useLocation} from 'react-router-dom';
import {createRegion} from 'region-react';
import {css} from '@emotion/css';
import {HomeLink} from '@/links/comatestack';
import {IconAiLab, IconSpace, IconTrainingCamp} from '@/icons/ievalueSidebar';
import {ChallengeLink, DatasourceSquareLink, TrainingLink, WorkspaceListLink} from '@/links';
import DatasourceSquare from '@/icons/ievalueSidebar/DatasourceSquare';
import {hotCss} from './useIEValueItems';
import {ChallengeIconComp} from './ChallengeIconComp';

const collapsedRegion = createRegion(false, {
    withLocalStorageKey: 'comatestack/LeftNavigationCollapsed',
});
export const ComateStackLeftNav = () => {
    const collapsed = collapsedRegion.useValue();
    const {pathname} = useLocation();
    const {templateCategory} = useSearchParams();

    const workspaceActive = pathname.startsWith('/comatestack/workspaceList');
    const homeActive = pathname.startsWith('/comatestack/lab');
    const squareActive = pathname.startsWith('/comatestack/template')
        || templateCategory === 'template';
    const challengeActive = pathname.startsWith('/comatestack/challenge')
        || templateCategory === 'challenge';
    const trainingActive = pathname.startsWith('/comatestack/training')
        || templateCategory === 'training';

    const activeBoldCss = css`
        font-weight: 600;
    `;

    const items = useMemo(
        () => [
            {
                title: '项目列表',
                shortTitle: '项目列表',
                to: WorkspaceListLink.toUrl(),
                icon: <IconSpace />,
                className: workspaceActive ? activeBoldCss : undefined,
                isActive: workspaceActive,
            },
            {
                title: '能力中心',
                shortTitle: '能力中心',
                to: HomeLink.toUrl(),
                icon: <IconAiLab />,
                className: homeActive ? `${hotCss} ${activeBoldCss}` : hotCss,
                isActive: homeActive,
            },
            {
                title: '资源广场',
                shortTitle: '资源广场',
                to: DatasourceSquareLink.toUrl(),
                icon: <DatasourceSquare />,
                className: squareActive ? activeBoldCss : undefined,
                isActive: squareActive,
            },
            {
                title: '挑战赛',
                shortTitle: '挑战赛',
                to: ChallengeLink.toUrl(),
                isActive: challengeActive,
                className: challengeActive ? activeBoldCss : undefined,
                icon: <ChallengeIconComp />,
            },
            {
                title: '训练营',
                shortTitle: '训练营',
                to: TrainingLink.toUrl(),
                isActive: trainingActive,
                className: trainingActive ? activeBoldCss : undefined,
                icon: <IconTrainingCamp />,
            },
        ],
        [activeBoldCss, challengeActive, homeActive, squareActive, trainingActive, workspaceActive]
    );

    return (
        <LeftNavigation
            collapsed={collapsed}
            onCollapse={collapsedRegion.set}
            logo={{
                icon: <IconHome />,
                title: 'Comate Stack',
                to: WorkspaceListLink.toUrl(),
            }}
            items={items}
        />
    );
};
